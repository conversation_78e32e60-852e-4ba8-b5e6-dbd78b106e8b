# 故障排除指南

## 当前问题：HTTP 500 错误

如果您看到 "保存错误: HTTP 500: {"success":false,"error":""}" 错误，这通常表示数据库连接问题。

## 诊断步骤

### 1. 检查环境变量
```bash
npm run check-env
```

### 2. 调试数据库连接
```bash
npm run debug-db
```

## 常见问题和解决方案

### 问题 1: ECONNREFUSED (连接被拒绝)

**症状**: 
- `npm run debug-db` 显示 "ECONNREFUSED"
- API 返回 HTTP 500 错误

**原因**: PostgreSQL 数据库没有运行或连接配置错误

**解决方案**:

#### 选项 A: 安装并启动本地 PostgreSQL

1. **安装 PostgreSQL**:
   - Windows: 下载并安装 [PostgreSQL](https://www.postgresql.org/download/windows/)
   - macOS: `brew install postgresql`
   - Linux: `sudo apt-get install postgresql`

2. **启动 PostgreSQL 服务**:
   - Windows: 在服务管理器中启动 PostgreSQL 服务
   - macOS: `brew services start postgresql`
   - Linux: `sudo systemctl start postgresql`

3. **创建数据库**:
   ```sql
   -- 连接到 PostgreSQL
   psql -U postgres
   
   -- 创建数据库
   CREATE DATABASE minesweeper_db;
   
   -- 创建用户（可选）
   CREATE USER myuser WITH PASSWORD 'mypassword';
   GRANT ALL PRIVILEGES ON DATABASE minesweeper_db TO myuser;
   ```

4. **更新 .env.local**:
   ```
   DATABASE_URL=postgresql://postgres:your_password@localhost:5432/minesweeper_db
   ```

#### 选项 B: 使用云数据库

如果不想安装本地 PostgreSQL，可以使用免费的云数据库：

1. **Supabase** (推荐):
   - 访问 [supabase.com](https://supabase.com)
   - 创建免费账户和项目
   - 在项目设置中找到数据库连接字符串
   - 更新 .env.local 文件

2. **Neon**:
   - 访问 [neon.tech](https://neon.tech)
   - 创建免费账户和数据库
   - 获取连接字符串

3. **Railway**:
   - 访问 [railway.app](https://railway.app)
   - 创建 PostgreSQL 数据库
   - 获取连接字符串

#### 选项 C: 使用 Docker

```bash
# 启动 PostgreSQL 容器
docker run --name minesweeper-postgres \
  -e POSTGRES_PASSWORD=mypassword \
  -e POSTGRES_DB=minesweeper_db \
  -p 5432:5432 \
  -d postgres:15

# 更新 .env.local
DATABASE_URL=postgresql://postgres:mypassword@localhost:5432/minesweeper_db
```

### 问题 2: 数据库表不存在

**症状**: 错误消息包含 "relation does not exist"

**解决方案**:
```bash
npm run init-db
```

### 问题 3: 认证失败

**症状**: 错误代码 28P01

**解决方案**:
- 检查用户名和密码是否正确
- 确保用户有访问数据库的权限

## 验证修复

完成上述步骤后：

1. **测试数据库连接**:
   ```bash
   npm run debug-db
   ```
   应该显示 "✅ Basic connection successful"

2. **初始化数据库表**:
   ```bash
   npm run init-db
   ```

3. **启动应用**:
   ```bash
   npm run dev
   ```

4. **测试游戏**:
   - 打开 http://localhost:3000
   - 输入玩家姓名
   - 完成一局游戏
   - 检查是否显示 "✅ 分数保存成功！"

## 开发模式下的临时解决方案

如果您暂时无法设置数据库，可以修改代码使其在没有数据库的情况下工作：

1. **禁用分数保存** (临时):
   在 `app/page.tsx` 中注释掉 `saveScore` 调用：
   ```typescript
   // saveScore(playerName, revealedCells);
   console.log('Score would be saved:', playerName, revealedCells);
   ```

2. **使用本地存储** (临时):
   可以将分数保存到浏览器的 localStorage 而不是数据库。

## 获取帮助

如果问题仍然存在：

1. 运行 `npm run debug-db` 并分享输出
2. 检查浏览器开发者工具的控制台
3. 检查 Next.js 开发服务器的终端输出
4. 分享您的 .env.local 文件内容（隐藏敏感信息）

## 常用命令总结

```bash
# 检查环境配置
npm run check-env

# 调试数据库
npm run debug-db

# 初始化数据库
npm run init-db

# 测试 API
npm run test-api

# 启动开发服务器
npm run dev
```
