import { savePlayerScore } from '@/lib/db';
import { NextResponse } from 'next/server';

export async function POST(request: Request) {
  try {
    console.log('API: Received save-score request');

    let requestData;
    try {
      requestData = await request.json();
      console.log('API: Request data:', requestData);
    } catch (parseError) {
      console.error('API: Failed to parse JSON:', parseError);
      return NextResponse.json({ success: false, error: 'Invalid JSON in request body' }, { status: 400 });
    }

    const { name, score } = requestData;

    // Basic input validation for name
    if (typeof name !== 'string' || name.trim() === '') {
      console.log('API: Invalid name:', name);
      return NextResponse.json({ success: false, error: 'Invalid player name' }, { status: 400 });
    }

    // Validate and parse score
    let validScore: number;
    if (typeof score === 'number') {
      validScore = score;
    } else if (typeof score === 'string') {
      validScore = parseInt(score, 10);
    } else {
      console.log('API: Invalid score type:', typeof score, score);
      return NextResponse.json({ success: false, error: 'Score must be a number or string' }, { status: 400 });
    }

    if (isNaN(validScore) || validScore < 0) {
      console.log('API: Invalid score value:', validScore);
      return NextResponse.json({ success: false, error: 'Invalid score value' }, { status: 400 });
    }

    console.log('API: Attempting to save score:', { name: name.trim(), score: validScore });
    await savePlayerScore(name.trim(), validScore);
    console.log('API: Score saved successfully');

    return NextResponse.json({ success: true, message: 'Score saved successfully' });
  } catch (error: any) {
    console.error('API error details:', {
      message: error?.message || 'No message',
      stack: error?.stack || 'No stack',
      name: error?.name || 'No name',
      error: error
    });

    // Extract error message with fallbacks
    let errorMessage = 'An unknown error occurred';
    if (error instanceof Error) {
      errorMessage = error.message || 'Error object has no message';
    } else if (typeof error === 'string') {
      errorMessage = error;
    } else if (error && typeof error === 'object') {
      errorMessage = error.message || error.toString() || 'Object error with no message';
    }

    return NextResponse.json(
      { success: false, error: errorMessage },
      { status: 500 }
    );
  }
}
