import { savePlayerScore } from '@/lib/db';
import { NextResponse } from 'next/server';

export async function POST(request: Request) {
  try {
    const { name, score: scoreString } = await request.json();

    // Basic input validation for name
    if (typeof name !== 'string' || name.trim() === '') {
      return NextResponse.json({ success: false, error: 'Invalid player name' }, { status: 400 });
    }

    // Validate and parse score
    const score = parseInt(scoreString);
    if (isNaN(score)) {
       return NextResponse.json({ success: false, error: 'Invalid score' }, { status: 400 });
    }

    await savePlayerScore(name, score);
    return NextResponse.json({ success: true });
  } catch (error: any) {
    console.error('API error:', error); // Log the error on the server side
    return NextResponse.json(
      { success: false, error: error instanceof Error ? error.message : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}
