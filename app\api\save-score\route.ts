import { savePlayerScore } from '@/lib/db';
import { NextResponse } from 'next/server';

export async function POST(request: Request) {
  try {
    const { name, score } = await request.json();

    // Basic input validation for name
    if (typeof name !== 'string' || name.trim() === '') {
      return NextResponse.json({ success: false, error: 'Invalid player name' }, { status: 400 });
    }

    // Validate and parse score
    let validScore: number;
    if (typeof score === 'number') {
      validScore = score;
    } else if (typeof score === 'string') {
      validScore = parseInt(score, 10);
    } else {
      return NextResponse.json({ success: false, error: 'Score must be a number or string' }, { status: 400 });
    }

    if (isNaN(validScore) || validScore < 0) {
      return NextResponse.json({ success: false, error: 'Invalid score value' }, { status: 400 });
    }

    await savePlayerScore(name.trim(), validScore);
    return NextResponse.json({ success: true, message: 'Score saved successfully' });
  } catch (error: any) {
    console.error('API error:', error); // Log the error on the server side
    return NextResponse.json(
      { success: false, error: error instanceof Error ? error.message : 'An unknown error occurred' },
      { status: 500 }
    );
  }
}
