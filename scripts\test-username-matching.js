#!/usr/bin/env node

/**
 * Username matching test script
 * Tests username matching logic in leaderboard
 */

const testCases = [
  {
    name: 'TestUser1',
    variations: ['TestUser1', 'testuser1', 'TESTUSER1', ' TestUser1 ', '  testuser1  ']
  },
  {
    name: '玩家小明',
    variations: ['玩家小明', ' 玩家小明 ', '  玩家小明  ']
  },
  {
    name: 'Player123',
    variations: ['Player123', 'player123', 'PLAYER123', ' Player123', 'Player123 ']
  }
];

async function testUsernameMatching() {
  const baseUrl = 'http://localhost:3000';
  
  console.log('🧪 Testing username matching in leaderboard...\n');
  
  for (const testCase of testCases) {
    console.log(`📝 Testing base name: "${testCase.name}"`);
    
    // Save scores with different name variations
    for (let i = 0; i < testCase.variations.length; i++) {
      const variation = testCase.variations[i];
      const score = 100 + i * 10; // Different scores for each variation
      
      console.log(`   Saving score for: "${variation}" (score: ${score})`);
      
      try {
        const response = await fetch(`${baseUrl}/api/save-score`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            name: variation,
            score: score,
            boardSize: 10,
            mineCount: 15,
            isWin: true,
            gameTimeSeconds: 120 + i * 10
          }),
        });
        
        const data = await response.json();
        
        if (response.ok && data.success) {
          console.log(`   ✅ Saved successfully`);
        } else {
          console.log(`   ❌ Failed: ${data.error || 'Unknown error'}`);
        }
      } catch (error) {
        console.log(`   ❌ Error: ${error.message}`);
      }
    }
    
    console.log('');
  }
  
  console.log('⏳ Waiting 2 seconds for database to update...\n');
  await new Promise(resolve => setTimeout(resolve, 2000));
  
  // Retrieve and analyze leaderboard
  console.log('📊 Retrieving leaderboard for analysis...');
  try {
    const response = await fetch(`${baseUrl}/api/leaderboard?limit=20`);
    const data = await response.json();
    
    if (response.ok && data.success) {
      console.log(`✅ Retrieved ${data.count} scores from leaderboard\n`);
      
      // Group by normalized names
      const nameGroups = {};
      
      data.data.forEach(entry => {
        const normalizedName = entry.playname?.trim().toLowerCase();
        if (!nameGroups[normalizedName]) {
          nameGroups[normalizedName] = [];
        }
        nameGroups[normalizedName].push(entry);
      });
      
      console.log('🔍 Username analysis:');
      console.log('─'.repeat(80));
      
      Object.keys(nameGroups).forEach(normalizedName => {
        const entries = nameGroups[normalizedName];
        console.log(`\nNormalized name: "${normalizedName}"`);
        console.log(`Found ${entries.length} entries:`);
        
        entries.forEach((entry, index) => {
          console.log(`  ${index + 1}. Original: "${entry.playname}" | Score: ${entry.score}`);
        });
        
        if (entries.length > 1) {
          console.log(`  ⚠️  Multiple entries found for same normalized name!`);
        }
      });
      
      console.log('\n📋 Matching logic test:');
      console.log('─'.repeat(80));
      
      // Test matching logic
      testCases.forEach(testCase => {
        console.log(`\nTesting matches for base name: "${testCase.name}"`);
        const baseName = testCase.name.trim().toLowerCase();
        
        const matches = data.data.filter(entry => 
          entry.playname?.trim().toLowerCase() === baseName
        );
        
        console.log(`Found ${matches.length} matches:`);
        matches.forEach((match, index) => {
          console.log(`  ${index + 1}. "${match.playname}" (Score: ${match.score})`);
        });
        
        // Test each variation
        testCase.variations.forEach(variation => {
          const shouldMatch = variation.trim().toLowerCase() === baseName;
          const actualMatches = data.data.filter(entry => 
            entry.playname?.trim().toLowerCase() === variation.trim().toLowerCase()
          );
          
          console.log(`  Variation "${variation}": ${actualMatches.length > 0 ? '✅' : '❌'} ${shouldMatch ? '(should match)' : '(should not match)'}`);
        });
      });
      
    } else {
      console.log(`❌ Failed to retrieve leaderboard: ${data.error || 'Unknown error'}`);
    }
  } catch (error) {
    console.log(`❌ Error retrieving leaderboard: ${error.message}`);
  }
  
  console.log('\n✅ Username matching test completed!');
  console.log('\n💡 Key findings:');
  console.log('   - Check if multiple entries exist for the same normalized name');
  console.log('   - Verify that matching logic handles spaces and case correctly');
  console.log('   - Look for any unexpected username variations in the database');
}

// Check if server is running
async function checkServer() {
  try {
    const response = await fetch('http://localhost:3000');
    if (response.ok) {
      console.log('✅ Server is running\n');
      return true;
    }
  } catch (error) {
    console.log('❌ Server is not running. Please start the development server first:');
    console.log('   npm run dev\n');
    return false;
  }
}

async function main() {
  const serverRunning = await checkServer();
  if (serverRunning) {
    await testUsernameMatching();
  }
}

main().catch(console.error);
