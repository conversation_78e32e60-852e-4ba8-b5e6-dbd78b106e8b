import { Pool } from 'pg';
import { env } from './env';

const pool = new Pool({
  connectionString: env.DATABASE_URL,
  max: env.DB_POOL_MAX,
  min: env.DB_POOL_MIN,
  connectionTimeoutMillis: env.DB_CONNECTION_TIMEOUT,
  idleTimeoutMillis: 30000,
  // Enable SSL for production environments
  ssl: env.isProduction ? { rejectUnauthorized: false } : false,
});

export async function savePlayerScore(name: string, score: number) {
  if (!name || typeof name !== 'string') {
    throw new Error('Player name is required and must be a string');
  }

  if (typeof score !== 'number' || isNaN(score) || score < 0) {
    throw new Error('Score must be a valid non-negative number');
  }

  const client = await pool.connect();
  try {
    await client.query(
      'INSERT INTO public.newtable (playname, score) VALUES ($1, $2)',
      [name, score]
    );
    console.log(`Score saved successfully: ${name} - ${score}`);
  } catch (error) {
    console.error('Database error:', error);
    if (error instanceof Error) {
      // Provide more specific error messages
      if (error.message.includes('connection')) {
        throw new Error('Database connection failed. Please check your database configuration.');
      } else if (error.message.includes('relation') && error.message.includes('does not exist')) {
        throw new Error('Database table does not exist. Please run the database setup script.');
      } else {
        throw new Error(`Database error: ${error.message}`);
      }
    }
    throw new Error('Unknown database error occurred');
  } finally {
    client.release();
  }
}

export async function getTopScores(limit = 10): Promise<{ playname: string; score: number }[]> {
  const client = await pool.connect();
  try {
    const res = await client.query(
      'SELECT playname, score FROM public.newtable ORDER BY score::integer DESC LIMIT $1',
      [limit]
    );
    // Map the results to ensure score is treated as a number
    return res.rows.map(row => ({
      playname: row.playname,
      score: parseInt(row.score, 10) // Ensure score is parsed as an integer
    }));
  } finally {
    client.release();
  }
}
