import { Pool } from 'pg';
import { env } from './env';

// Check if we're in a development environment without a database
const isDevelopmentWithoutDB = env.isDevelopment && env.DATABASE_URL.includes('username:password@localhost');

let pool: Pool | null = null;

if (!isDevelopmentWithoutDB) {
  pool = new Pool({
    connectionString: env.DATABASE_URL,
    max: env.DB_POOL_MAX,
    min: env.DB_POOL_MIN,
    connectionTimeoutMillis: env.DB_CONNECTION_TIMEOUT,
    idleTimeoutMillis: 30000,
    // Enable SSL for production environments
    ssl: env.isProduction ? { rejectUnauthorized: false } : false,
  });
}

export async function savePlayerScore(
  name: string,
  score: number,
  boardSize: number = 10,
  mineCount: number = 15,
  isWin: boolean = false,
  gameTimeSeconds: number = 0
) {
  console.log('DB: savePlayerScore called with:', {
    name, score, boardSize, mineCount, isWin, gameTimeSeconds,
    nameType: typeof name, scoreType: typeof score
  });

  if (!name || typeof name !== 'string') {
    const error = 'Player name is required and must be a string';
    console.error('DB: Validation error:', error);
    throw new Error(error);
  }

  if (typeof score !== 'number' || isNaN(score) || score < 0) {
    const error = 'Score must be a valid non-negative number';
    console.error('DB: Validation error:', error);
    throw new Error(error);
  }

  // Check if database is available
  if (!pool) {
    console.log('DB: No database configured, using mock save');
    console.log(`DB: Mock save - Player: ${name}, Score: ${score}, Board: ${boardSize}x${boardSize}, Mines: ${mineCount}, Win: ${isWin}, Time: ${gameTimeSeconds}s`);
    return; // Return successfully without actually saving
  }

  console.log('DB: Attempting to connect to database...');
  let client;
  try {
    client = await pool.connect();
    console.log('DB: Connected successfully');
  } catch (connectionError) {
    console.error('DB: Connection failed:', connectionError);
    throw new Error(`Database connection failed: ${connectionError instanceof Error ? connectionError.message : 'Unknown connection error'}`);
  }

  try {
    console.log('DB: Executing INSERT query...');

    // Try new table structure first, fall back to old structure if needed
    try {
      await client.query(
        'INSERT INTO public.newtable (playname, score, board_size, mine_count, is_win, game_time_seconds) VALUES ($1, $2, $3, $4, $5, $6)',
        [name, score, boardSize, mineCount, isWin, gameTimeSeconds]
      );
      console.log(`DB: Score saved successfully with full details: ${name} - ${score}`);
    } catch (detailedError) {
      // If the detailed insert fails (old table structure), try simple insert
      console.log('DB: Falling back to simple insert (old table structure)');
      await client.query(
        'INSERT INTO public.newtable (playname, score) VALUES ($1, $2)',
        [name, score]
      );
      console.log(`DB: Score saved successfully (simple): ${name} - ${score}`);
    }
  } catch (error) {
    console.error('DB: Query execution error:', {
      message: error instanceof Error ? error.message : 'No message',
      code: (error as any)?.code || 'No code',
      detail: (error as any)?.detail || 'No detail',
      error: error
    });

    if (error instanceof Error) {
      // Provide more specific error messages
      if (error.message.includes('connection')) {
        throw new Error('Database connection failed. Please check your database configuration.');
      } else if (error.message.includes('relation') && error.message.includes('does not exist')) {
        throw new Error('Database table "newtable" does not exist. Please run: npm run init-db');
      } else if ((error as any).code === '42P01') {
        throw new Error('Database table "newtable" does not exist. Please run: npm run init-db');
      } else if ((error as any).code === '28P01') {
        throw new Error('Database authentication failed. Please check your credentials.');
      } else if ((error as any).code === 'ECONNREFUSED') {
        throw new Error('Database connection refused. Please check if PostgreSQL is running.');
      } else {
        throw new Error(`Database error: ${error.message}`);
      }
    }
    throw new Error(`Unknown database error: ${JSON.stringify(error)}`);
  } finally {
    if (client) {
      client.release();
      console.log('DB: Connection released');
    }
  }
}

export async function getTopScores(limit = 10): Promise<any[]> {
  // Check if database is available
  if (!pool) {
    console.log('DB: No database configured, returning mock scores');
    return [
      {
        playname: 'Demo Player 1',
        score: 350,
        board_size: 10,
        mine_count: 15,
        is_win: true,
        game_time_seconds: 120
      },
      {
        playname: 'Demo Player 2',
        score: 285,
        board_size: 8,
        mine_count: 10,
        is_win: true,
        game_time_seconds: 95
      },
      {
        playname: 'Demo Player 3',
        score: 170,
        board_size: 12,
        mine_count: 20,
        is_win: false,
        game_time_seconds: 200
      }
    ];
  }

  console.log('DB: Fetching top scores from database...');
  const client = await pool.connect();
  try {
    // Try to get all fields first (new table structure)
    let res;
    try {
      res = await client.query(
        `SELECT playname, score, board_size, mine_count, is_win, game_time_seconds, created_at
         FROM public.newtable
         ORDER BY score DESC, created_at DESC
         LIMIT $1`,
        [limit]
      );
      console.log('DB: Retrieved scores with full details:', res.rows.length);
    } catch (detailedError) {
      // Fall back to simple query for old table structure
      console.log('DB: Falling back to simple query (old table structure)');
      res = await client.query(
        'SELECT playname, score, created_at FROM public.newtable ORDER BY score DESC, created_at DESC LIMIT $1',
        [limit]
      );
      console.log('DB: Retrieved scores (simple):', res.rows.length);
    }

    // Map the results to ensure score is treated as a number and add default values
    return res.rows.map(row => ({
      playname: row.playname,
      score: parseInt(row.score, 10),
      board_size: row.board_size || 10,
      mine_count: row.mine_count || 15,
      is_win: row.is_win || false,
      game_time_seconds: row.game_time_seconds || 0,
      created_at: row.created_at
    }));
  } catch (error) {
    console.error('DB: Error fetching top scores:', error);
    throw error;
  } finally {
    client.release();
  }
}
