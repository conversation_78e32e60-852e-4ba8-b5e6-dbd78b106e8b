import { Pool } from 'pg';
import { env } from './env';

const pool = new Pool({
  connectionString: env.DATABASE_URL,
  max: env.DB_POOL_MAX,
  min: env.DB_POOL_MIN,
  connectionTimeoutMillis: env.DB_CONNECTION_TIMEOUT,
  idleTimeoutMillis: 30000,
  // Enable SSL for production environments
  ssl: env.isProduction ? { rejectUnauthorized: false } : false,
});

export async function savePlayerScore(name: string, score: number) {
  const client = await pool.connect();
  try {
    await client.query(
      'INSERT INTO public.newtable (playname, score) VALUES ($1, $2)',
      [name, score]
    );
  } catch (error) {
    console.error('Database error:', error); // Log the error on the server side
    throw error; // Re-throw the error to be caught by the API route
  } finally {
    client.release();
  }
}

export async function getTopScores(limit = 10): Promise<{ playname: string; score: number }[]> {
  const client = await pool.connect();
  try {
    const res = await client.query(
      'SELECT playname, score FROM public.newtable ORDER BY score::integer DESC LIMIT $1',
      [limit]
    );
    // Map the results to ensure score is treated as a number
    return res.rows.map(row => ({
      playname: row.playname,
      score: parseInt(row.score, 10) // Ensure score is parsed as an integer
    }));
  } finally {
    client.release();
  }
}
