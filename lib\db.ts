import { Pool } from 'pg';
import { env } from './env';

// Check if we're in a development environment without a database
const isDevelopmentWithoutDB = env.isDevelopment && env.DATABASE_URL.includes('username:password@localhost');

let pool: Pool | null = null;

if (!isDevelopmentWithoutDB) {
  pool = new Pool({
    connectionString: env.DATABASE_URL,
    max: env.DB_POOL_MAX,
    min: env.DB_POOL_MIN,
    connectionTimeoutMillis: env.DB_CONNECTION_TIMEOUT,
    idleTimeoutMillis: 30000,
    // Enable SSL for production environments
    ssl: env.isProduction ? { rejectUnauthorized: false } : false,
  });
}

export async function savePlayerScore(name: string, score: number) {
  console.log('DB: savePlayerScore called with:', { name, score, nameType: typeof name, scoreType: typeof score });

  if (!name || typeof name !== 'string') {
    const error = 'Player name is required and must be a string';
    console.error('DB: Validation error:', error);
    throw new Error(error);
  }

  if (typeof score !== 'number' || isNaN(score) || score < 0) {
    const error = 'Score must be a valid non-negative number';
    console.error('DB: Validation error:', error);
    throw new Error(error);
  }

  // Check if database is available
  if (!pool) {
    console.log('DB: No database configured, using mock save');
    console.log(`DB: Mock save - Player: ${name}, Score: ${score}`);
    return; // Return successfully without actually saving
  }

  console.log('DB: Attempting to connect to database...');
  let client;
  try {
    client = await pool.connect();
    console.log('DB: Connected successfully');
  } catch (connectionError) {
    console.error('DB: Connection failed:', connectionError);
    throw new Error(`Database connection failed: ${connectionError instanceof Error ? connectionError.message : 'Unknown connection error'}`);
  }

  try {
    console.log('DB: Executing INSERT query...');
    await client.query(
      'INSERT INTO public.newtable (playname, score) VALUES ($1, $2)',
      [name, score]
    );
    console.log(`DB: Score saved successfully: ${name} - ${score}`);
  } catch (error) {
    console.error('DB: Query execution error:', {
      message: error instanceof Error ? error.message : 'No message',
      code: (error as any)?.code || 'No code',
      detail: (error as any)?.detail || 'No detail',
      error: error
    });

    if (error instanceof Error) {
      // Provide more specific error messages
      if (error.message.includes('connection')) {
        throw new Error('Database connection failed. Please check your database configuration.');
      } else if (error.message.includes('relation') && error.message.includes('does not exist')) {
        throw new Error('Database table "newtable" does not exist. Please run: npm run init-db');
      } else if ((error as any).code === '42P01') {
        throw new Error('Database table "newtable" does not exist. Please run: npm run init-db');
      } else if ((error as any).code === '28P01') {
        throw new Error('Database authentication failed. Please check your credentials.');
      } else if ((error as any).code === 'ECONNREFUSED') {
        throw new Error('Database connection refused. Please check if PostgreSQL is running.');
      } else {
        throw new Error(`Database error: ${error.message}`);
      }
    }
    throw new Error(`Unknown database error: ${JSON.stringify(error)}`);
  } finally {
    if (client) {
      client.release();
      console.log('DB: Connection released');
    }
  }
}

export async function getTopScores(limit = 10): Promise<{ playname: string; score: number }[]> {
  // Check if database is available
  if (!pool) {
    console.log('DB: No database configured, returning mock scores');
    return [
      { playname: 'Demo Player 1', score: 100 },
      { playname: 'Demo Player 2', score: 85 },
      { playname: 'Demo Player 3', score: 70 }
    ];
  }

  const client = await pool.connect();
  try {
    const res = await client.query(
      'SELECT playname, score FROM public.newtable ORDER BY score::integer DESC LIMIT $1',
      [limit]
    );
    // Map the results to ensure score is treated as a number
    return res.rows.map(row => ({
      playname: row.playname,
      score: parseInt(row.score, 10) // Ensure score is parsed as an integer
    }));
  } finally {
    client.release();
  }
}
