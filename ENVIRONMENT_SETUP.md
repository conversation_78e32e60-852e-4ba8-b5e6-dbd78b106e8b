# 环境变量配置指南

本项目使用环境变量来管理配置，确保敏感信息不会被提交到版本控制系统中。

## 快速开始

1. 复制示例环境文件：
   ```bash
   cp .env.example .env.local
   ```

2. 编辑 `.env.local` 文件，填入您的实际配置值。

## 环境变量说明

### 必需的环境变量

#### `DATABASE_URL`
PostgreSQL 数据库连接字符串。

**格式：** `postgresql://username:password@host:port/database`

**示例：**
```
DATABASE_URL=postgresql://myuser:mypassword@localhost:5432/minesweeper_db
```

### 可选的环境变量

#### `NODE_ENV`
应用程序运行环境。

**可选值：** `development`, `production`, `test`
**默认值：** `development`

#### `NEXT_PUBLIC_APP_URL`
应用程序的基础 URL，用于 API 调用和链接生成。

**默认值：** `http://localhost:3000`

#### `DB_POOL_MAX`
数据库连接池的最大连接数。

**默认值：** `20`

#### `DB_POOL_MIN`
数据库连接池的最小连接数。

**默认值：** `5`

#### `DB_CONNECTION_TIMEOUT`
数据库连接超时时间（毫秒）。

**默认值：** `30000` (30秒)

#### `NEXTAUTH_SECRET`
用于会话管理的密钥（如果使用 NextAuth.js）。

**生成方法：**
```bash
openssl rand -base64 32
```

#### `NEXTAUTH_URL`
NextAuth.js 的基础 URL（如果使用 NextAuth.js）。

**默认值：** `http://localhost:3000`

## 环境文件说明

### `.env.local`
本地开发环境的配置文件，包含实际的配置值。此文件不会被提交到版本控制系统。

### `.env.example`
示例配置文件，展示所有可用的环境变量。此文件会被提交到版本控制系统，作为配置参考。

## 数据库设置

### 本地 PostgreSQL 设置

1. 安装 PostgreSQL
2. 创建数据库：
   ```sql
   CREATE DATABASE minesweeper_db;
   ```

3. 创建用户（可选）：
   ```sql
   CREATE USER myuser WITH PASSWORD 'mypassword';
   GRANT ALL PRIVILEGES ON DATABASE minesweeper_db TO myuser;
   ```

4. 创建表：
   ```sql
   CREATE TABLE public.newtable (
     id SERIAL PRIMARY KEY,
     playname VARCHAR(255) NOT NULL,
     score INTEGER NOT NULL,
     created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
   );
   ```

### 云数据库设置

如果使用云数据库服务（如 Heroku Postgres、AWS RDS、Google Cloud SQL），请从服务提供商获取连接字符串。

## 生产环境部署

### Vercel 部署

1. 在 Vercel 项目设置中添加环境变量
2. 确保 `NODE_ENV` 设置为 `production`
3. 配置生产数据库的 `DATABASE_URL`

### 其他平台

根据部署平台的文档设置环境变量。

## 安全注意事项

1. **永远不要**将包含敏感信息的 `.env.local` 文件提交到版本控制系统
2. 在生产环境中使用强密码和安全的连接字符串
3. 定期轮换密钥和密码
4. 使用 HTTPS 连接数据库（生产环境）

## 故障排除

### 常见错误

#### "DATABASE_URL environment variable is required"
确保 `.env.local` 文件存在且包含有效的 `DATABASE_URL`。

#### 数据库连接失败
1. 检查数据库服务是否运行
2. 验证连接字符串的格式和凭据
3. 确保数据库和表已创建

#### 环境变量未加载
1. 确保文件名为 `.env.local`（注意前缀点）
2. 重启开发服务器
3. 检查文件是否在项目根目录

## 开发工具

项目包含环境变量验证工具（`lib/env.ts`），会在应用启动时自动验证所有必需的环境变量。
