/**
 * Environment variable validation and configuration
 */

// Required environment variables
const requiredEnvVars = [
  'DATABASE_URL',
] as const;

// Optional environment variables with defaults
const optionalEnvVars = {
  NODE_ENV: 'development',
  NEXT_PUBLIC_APP_URL: 'http://localhost:3000',
  DB_POOL_MAX: '20',
  DB_POOL_MIN: '5',
  DB_CONNECTION_TIMEOUT: '30000',
} as const;

/**
 * Validates that all required environment variables are present
 * @throws Error if any required environment variable is missing
 */
export function validateEnv(): void {
  const missingVars: string[] = [];

  for (const envVar of requiredEnvVars) {
    if (!process.env[envVar]) {
      missingVars.push(envVar);
    }
  }

  if (missingVars.length > 0) {
    throw new Error(
      `Missing required environment variables: ${missingVars.join(', ')}\n` +
      'Please check your .env.local file and ensure all required variables are set.'
    );
  }
}

/**
 * Gets an environment variable with a fallback default
 */
export function getEnvVar(key: string, defaultValue?: string): string {
  const value = process.env[key];
  if (value === undefined) {
    if (defaultValue !== undefined) {
      return defaultValue;
    }
    throw new Error(`Environment variable ${key} is not set and no default provided`);
  }
  return value;
}

/**
 * Gets a numeric environment variable with validation
 */
export function getEnvNumber(key: string, defaultValue?: number): number {
  const value = process.env[key];
  if (value === undefined) {
    if (defaultValue !== undefined) {
      return defaultValue;
    }
    throw new Error(`Environment variable ${key} is not set and no default provided`);
  }
  
  const parsed = parseInt(value, 10);
  if (isNaN(parsed)) {
    throw new Error(`Environment variable ${key} must be a valid number, got: ${value}`);
  }
  
  return parsed;
}

/**
 * Gets a boolean environment variable
 */
export function getEnvBoolean(key: string, defaultValue?: boolean): boolean {
  const value = process.env[key];
  if (value === undefined) {
    if (defaultValue !== undefined) {
      return defaultValue;
    }
    throw new Error(`Environment variable ${key} is not set and no default provided`);
  }
  
  return value.toLowerCase() === 'true';
}

/**
 * Environment configuration object with validated values
 */
export const env = {
  // Database
  DATABASE_URL: getEnvVar('DATABASE_URL'),
  DB_POOL_MAX: getEnvNumber('DB_POOL_MAX', 20),
  DB_POOL_MIN: getEnvNumber('DB_POOL_MIN', 5),
  DB_CONNECTION_TIMEOUT: getEnvNumber('DB_CONNECTION_TIMEOUT', 30000),
  
  // Application
  NODE_ENV: getEnvVar('NODE_ENV', 'development') as 'development' | 'production' | 'test',
  NEXT_PUBLIC_APP_URL: getEnvVar('NEXT_PUBLIC_APP_URL', 'http://localhost:3000'),
  
  // Security
  NEXTAUTH_SECRET: process.env.NEXTAUTH_SECRET,
  NEXTAUTH_URL: process.env.NEXTAUTH_URL,
  
  // Computed values
  get isDevelopment() {
    return this.NODE_ENV === 'development';
  },
  get isProduction() {
    return this.NODE_ENV === 'production';
  },
  get isTest() {
    return this.NODE_ENV === 'test';
  },
};

// Validate environment on module load (only in Node.js environment)
if (typeof window === 'undefined') {
  validateEnv();
}
