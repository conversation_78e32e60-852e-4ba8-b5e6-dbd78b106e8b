# 扫雷游戏 (Minesweeper Game)

这是一个使用 [Next.js](https://nextjs.org) 构建的扫雷游戏项目，支持玩家分数保存和排行榜功能。

## 功能特性

- 🎮 经典扫雷游戏玩法
- 📊 智能分数计算系统
- 🏆 完整排行榜功能
- 💾 PostgreSQL 数据库存储
- 🎨 现代化 UI 设计（Tailwind CSS）
- 🏅 多难度支持和成就系统
- 📱 响应式设计，支持移动设备

## 环境配置

在开始之前，您需要配置环境变量：

1. 复制环境变量示例文件：
   ```bash
   cp .env.example .env.local
   ```

2. 编辑 `.env.local` 文件，配置您的数据库连接：
   ```
   DATABASE_URL=postgresql://username:password@localhost:5432/minesweeper_db
   ```

3. 详细的环境变量配置说明请参考 [ENVIRONMENT_SETUP.md](./ENVIRONMENT_SETUP.md)

## 数据库设置

创建 PostgreSQL 数据库和表：

```sql
CREATE DATABASE minesweeper_db;

CREATE TABLE public.newtable (
  id SERIAL PRIMARY KEY,
  playname VARCHAR(255) NOT NULL,
  score INTEGER NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## 快速开始

### 1. 安装依赖
```bash
npm install
```

### 2. 配置环境变量
```bash
cp .env.example .env.local
# 编辑 .env.local 文件，配置数据库连接
```

### 3. 初始化数据库
```bash
npm run init-db
```

### 4. 启动开发服务器

```bash
npm run dev
# or
yarn dev
# or
pnpm dev
# or
bun dev
```

打开 [http://localhost:3000](http://localhost:3000) 在浏览器中查看结果。

### 5. 测试功能
```bash
# 测试环境配置
npm run check-env

# 测试分数保存功能
npm run test-score-save
```

## 游戏玩法

1. **输入玩家姓名**：在主菜单输入您的姓名
2. **选择难度**：
   - 简单 (8×8, 10个地雷)
   - 中等 (10×10, 15个地雷)
   - 困难 (12×12, 20个地雷)
3. **开始游戏**：左键翻开格子，右键标记地雷
4. **查看排行榜**：游戏结束后自动显示，或点击排行榜按钮

## 分数系统

- **基础分数**：简单100分，中等200分，困难300分
- **地雷奖励**：每个地雷+5分
- **速度奖励**：完成越快分数越高
- **完美奖励**：无错误标记+50分

## 排行榜功能

- 🏆 实时排行榜显示
- 📊 详细游戏统计信息
- 🥇 前三名特殊标识
- 📱 响应式设计

## 技术栈

- **前端框架**: Next.js 15 with App Router
- **UI 库**: React 19
- **样式**: Tailwind CSS 4
- **数据库**: PostgreSQL
- **字体**: [Geist](https://vercel.com/font) - Vercel 的现代字体

## 项目结构

```
my-app/
├── app/                    # Next.js App Router 页面
│   ├── api/               # API 路由
│   ├── globals.css        # 全局样式
│   ├── layout.tsx         # 根布局
│   └── page.tsx           # 主页面（扫雷游戏）
├── lib/                   # 共享工具和配置
│   ├── db.ts             # 数据库连接和查询
│   └── env.ts            # 环境变量验证
├── .env.example          # 环境变量示例
└── ENVIRONMENT_SETUP.md  # 环境配置详细说明
```

## 了解更多

要了解更多关于 Next.js 的信息，请查看以下资源：

- [Next.js 文档](https://nextjs.org/docs) - 学习 Next.js 功能和 API
- [Next.js 教程](https://nextjs.org/learn) - 交互式 Next.js 教程

## 部署

### Vercel 部署

最简单的部署方式是使用 [Vercel 平台](https://vercel.com/new?utm_medium=default-template&filter=next.js&utm_source=create-next-app&utm_campaign=create-next-app-readme)：

1. 将代码推送到 GitHub
2. 在 Vercel 中导入项目
3. 配置环境变量（特别是 `DATABASE_URL`）
4. 部署

### 其他平台

查看 [Next.js 部署文档](https://nextjs.org/docs/app/building-your-application/deploying) 了解更多部署选项。

## 贡献

欢迎提交 Issue 和 Pull Request！
