# 扫雷游戏 (Minesweeper Game)

这是一个使用 [Next.js](https://nextjs.org) 构建的扫雷游戏项目，支持玩家分数保存和排行榜功能。

## 功能特性

- 🎮 经典扫雷游戏玩法
- 📊 玩家分数保存
- 🏆 排行榜系统
- 💾 PostgreSQL 数据库存储
- 🎨 现代化 UI 设计（Tailwind CSS）

## 环境配置

在开始之前，您需要配置环境变量：

1. 复制环境变量示例文件：
   ```bash
   cp .env.example .env.local
   ```

2. 编辑 `.env.local` 文件，配置您的数据库连接：
   ```
   DATABASE_URL=postgresql://username:password@localhost:5432/minesweeper_db
   ```

3. 详细的环境变量配置说明请参考 [ENVIRONMENT_SETUP.md](./ENVIRONMENT_SETUP.md)

## 数据库设置

创建 PostgreSQL 数据库和表：

```sql
CREATE DATABASE minesweeper_db;

CREATE TABLE public.newtable (
  id SERIAL PRIMARY KEY,
  playname VARCHAR(255) NOT NULL,
  score INTEGER NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## 开始使用

首先，安装依赖：

```bash
npm install
```

然后运行开发服务器：

```bash
npm run dev
# or
yarn dev
# or
pnpm dev
# or
bun dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

You can start editing the page by modifying `app/page.tsx`. The page auto-updates as you edit the file.

## 技术栈

- **前端框架**: Next.js 15 with App Router
- **UI 库**: React 19
- **样式**: Tailwind CSS 4
- **数据库**: PostgreSQL
- **字体**: [Geist](https://vercel.com/font) - Vercel 的现代字体

## 项目结构

```
my-app/
├── app/                    # Next.js App Router 页面
│   ├── api/               # API 路由
│   ├── globals.css        # 全局样式
│   ├── layout.tsx         # 根布局
│   └── page.tsx           # 主页面（扫雷游戏）
├── lib/                   # 共享工具和配置
│   ├── db.ts             # 数据库连接和查询
│   └── env.ts            # 环境变量验证
├── .env.example          # 环境变量示例
└── ENVIRONMENT_SETUP.md  # 环境配置详细说明
```

## 了解更多

要了解更多关于 Next.js 的信息，请查看以下资源：

- [Next.js 文档](https://nextjs.org/docs) - 学习 Next.js 功能和 API
- [Next.js 教程](https://nextjs.org/learn) - 交互式 Next.js 教程

## 部署

### Vercel 部署

最简单的部署方式是使用 [Vercel 平台](https://vercel.com/new?utm_medium=default-template&filter=next.js&utm_source=create-next-app&utm_campaign=create-next-app-readme)：

1. 将代码推送到 GitHub
2. 在 Vercel 中导入项目
3. 配置环境变量（特别是 `DATABASE_URL`）
4. 部署

### 其他平台

查看 [Next.js 部署文档](https://nextjs.org/docs/app/building-your-application/deploying) 了解更多部署选项。

## 贡献

欢迎提交 Issue 和 Pull Request！
