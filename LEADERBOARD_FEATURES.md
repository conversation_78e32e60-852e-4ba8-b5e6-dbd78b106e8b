# 排行榜功能实现总结

## 功能概述

为扫雷游戏添加了完整的排行榜系统，包括智能分数计算、详细游戏记录和美观的排行榜界面。

## 主要功能

### 1. 智能分数计算系统

#### 分数计算公式
- **基础分数**：
  - 简单难度 (8×8)：100分
  - 中等难度 (10×10)：200分
  - 困难难度 (12×12)：300分

- **地雷奖励**：每个地雷 +5分

- **速度奖励**：`1000 / 游戏时间(秒)`，完成越快分数越高

- **完美奖励**：无错误标记 +50分

- **失败分数**：基于翻开的安全格子数量

#### 实现位置
- `app/page.tsx` - `calculateScore` 函数

### 2. 数据库扩展

#### 新增字段
```sql
CREATE TABLE public.newtable (
  id SERIAL PRIMARY KEY,
  playname VARCHAR(255) NOT NULL,
  score INTEGER NOT NULL,
  board_size INTEGER DEFAULT 10,      -- 棋盘大小
  mine_count INTEGER DEFAULT 15,      -- 地雷数量
  is_win BOOLEAN DEFAULT false,       -- 是否获胜
  game_time_seconds INTEGER DEFAULT 0, -- 游戏时间(秒)
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### 向后兼容
- 支持旧表结构的自动回退
- 新字段有默认值，不影响现有数据

### 3. API 端点

#### 保存分数 API (`/api/save-score`)
**请求参数**：
```json
{
  "name": "玩家姓名",
  "score": 350,
  "boardSize": 10,
  "mineCount": 15,
  "isWin": true,
  "gameTimeSeconds": 120
}
```

#### 排行榜 API (`/api/leaderboard`)
**查询参数**：
- `limit`: 返回记录数量 (1-100，默认10)

**响应格式**：
```json
{
  "success": true,
  "data": [
    {
      "playname": "玩家1",
      "score": 350,
      "board_size": 10,
      "mine_count": 15,
      "is_win": true,
      "game_time_seconds": 120
    }
  ],
  "count": 1
}
```

### 4. 用户界面改进

#### 主菜单排行榜
- **位置**：主菜单界面
- **触发**：点击 "🏆 排行榜" 按钮
- **功能**：
  - 显示前10名玩家
  - 包含排名、玩家名、难度、分数
  - 显示游戏时间和获胜状态
  - 前三名特殊标识 (🥇🥈🥉)
  - 刷新功能

#### 游戏中排行榜
- **位置**：游戏界面
- **功能**：
  - 紧凑显示前5名
  - 高亮当前玩家记录
  - 实时刷新

#### 分数说明
- **位置**：主菜单游戏说明区域
- **内容**：详细的分数计算规则

### 5. 游戏体验改进

#### 时间记录
- 游戏开始时记录时间戳
- 游戏结束时计算总用时
- 用于分数计算和排行榜显示

#### 自动刷新
- 分数保存成功后自动刷新排行榜
- 确保最新数据显示

#### 视觉反馈
- 加载动画
- 排名徽章
- 获胜标识
- 当前玩家高亮

## 技术实现

### 前端组件
- **状态管理**：React useState 管理排行榜状态
- **数据获取**：fetch API 调用后端接口
- **UI 组件**：Tailwind CSS 样式化组件

### 后端 API
- **Next.js API Routes**：处理 HTTP 请求
- **错误处理**：详细的错误信息和状态码
- **数据验证**：输入参数验证

### 数据库操作
- **PostgreSQL**：关系型数据库存储
- **连接池**：pg 库连接池管理
- **事务安全**：错误处理和连接释放

## 使用方法

### 查看排行榜
1. **主菜单**：点击 "🏆 排行榜" 按钮
2. **游戏中**：点击游戏界面的 "🏆 排行榜" 按钮

### 提升排名
1. **选择高难度**：困难模式基础分数更高
2. **快速完成**：时间越短速度奖励越高
3. **精确标记**：避免错误标记获得完美奖励
4. **多练习**：熟练度提升有助于获得高分

### 数据库管理
```bash
# 初始化/更新数据库表结构
npm run init-db

# 调试数据库连接
npm run debug-db
```

## 排行榜特色

### 多维度排名
- 综合分数排名
- 显示游戏难度
- 显示完成时间
- 区分获胜/失败

### 用户友好
- 直观的排名显示
- 清晰的难度标识
- 时间格式化显示
- 当前玩家高亮

### 响应式设计
- 适配不同屏幕尺寸
- 移动设备友好
- 加载状态提示

## 未来扩展

### 可能的改进
1. **分类排行榜**：按难度分别排名
2. **时间排行榜**：最快完成时间排名
3. **个人统计**：个人历史记录和统计
4. **成就系统**：解锁成就和徽章
5. **社交功能**：好友排行榜
6. **数据导出**：排行榜数据导出功能

### 技术优化
1. **缓存机制**：Redis 缓存热门数据
2. **分页加载**：大量数据的分页显示
3. **实时更新**：WebSocket 实时排行榜
4. **数据分析**：游戏数据统计和分析

## 总结

排行榜功能为扫雷游戏增加了竞技性和趣味性，通过智能的分数计算系统和美观的界面设计，为玩家提供了更好的游戏体验。系统设计考虑了扩展性和向后兼容性，为未来的功能扩展奠定了基础。
