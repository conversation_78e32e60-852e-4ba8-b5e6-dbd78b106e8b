{"name": "my-app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "check-env": "node scripts/check-env.js", "test-api": "node scripts/test-api.js", "init-db": "node scripts/init-db.js", "debug-db": "node scripts/debug-db.js", "test-score-save": "node scripts/test-score-save.js", "test-username": "node scripts/test-username-matching.js", "setup": "npm run check-env && npm run dev"}, "dependencies": {"@types/pg": "^8.15.4", "next": "15.3.4", "pg": "^8.16.2", "react": "^19.0.0", "react-dom": "^19.0.0"}, "devDependencies": {"@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "dotenv": "^16.5.0", "tailwindcss": "^4", "typescript": "^5"}}