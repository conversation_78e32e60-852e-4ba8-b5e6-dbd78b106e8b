#!/usr/bin/env node

/**
 * Environment variable validation script
 * Run this script to check if all required environment variables are properly configured
 */

const fs = require('fs');
const path = require('path');

// Load environment variables from .env.local
const envPath = path.join(process.cwd(), '.env.local');

if (!fs.existsSync(envPath)) {
  console.error('❌ .env.local file not found!');
  console.log('📝 Please copy .env.example to .env.local and configure your environment variables.');
  console.log('   Command: cp .env.example .env.local');
  process.exit(1);
}

// Load .env.local
require('dotenv').config({ path: envPath });

const requiredVars = [
  'DATABASE_URL',
];

const optionalVars = [
  'NODE_ENV',
  'NEXT_PUBLIC_APP_URL',
  'DB_POOL_MAX',
  'DB_POOL_MIN',
  'DB_CONNECTION_TIMEOUT',
  'NEXTAUTH_SECRET',
  'NEXTAUTH_URL',
];

console.log('🔍 Checking environment variables...\n');

// Check required variables
let hasErrors = false;
console.log('📋 Required variables:');
for (const varName of requiredVars) {
  const value = process.env[varName];
  if (!value) {
    console.log(`❌ ${varName}: Missing`);
    hasErrors = true;
  } else {
    // Mask sensitive values
    const displayValue = varName.includes('URL') || varName.includes('SECRET') 
      ? value.substring(0, 10) + '...' 
      : value;
    console.log(`✅ ${varName}: ${displayValue}`);
  }
}

console.log('\n📋 Optional variables:');
for (const varName of optionalVars) {
  const value = process.env[varName];
  if (value) {
    const displayValue = varName.includes('SECRET') 
      ? value.substring(0, 10) + '...' 
      : value;
    console.log(`✅ ${varName}: ${displayValue}`);
  } else {
    console.log(`⚠️  ${varName}: Not set (using default)`);
  }
}

// Test database connection if DATABASE_URL is provided
if (process.env.DATABASE_URL && !hasErrors) {
  console.log('\n🔌 Testing database connection...');
  
  const { Pool } = require('pg');
  const pool = new Pool({
    connectionString: process.env.DATABASE_URL,
    connectionTimeoutMillis: 5000,
  });

  pool.connect()
    .then(client => {
      console.log('✅ Database connection successful!');
      client.release();
      pool.end();
    })
    .catch(err => {
      console.log('❌ Database connection failed:');
      console.log(`   ${err.message}`);
      hasErrors = true;
    });
} else if (hasErrors) {
  console.log('\n⏭️  Skipping database connection test due to missing required variables.');
}

if (hasErrors) {
  console.log('\n❌ Environment validation failed!');
  console.log('📖 Please check ENVIRONMENT_SETUP.md for detailed configuration instructions.');
  process.exit(1);
} else {
  console.log('\n✅ Environment validation passed!');
  console.log('🚀 You can now run the development server with: npm run dev');
}
