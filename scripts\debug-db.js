#!/usr/bin/env node

/**
 * Database debugging script
 * Tests database connection and operations step by step
 */

require('dotenv').config({ path: '.env.local' });

async function debugDatabase() {
  console.log('🔍 Database Debug Information\n');
  
  // Check environment variables
  console.log('📋 Environment Variables:');
  console.log('DATABASE_URL:', process.env.DATABASE_URL ? 'Set' : 'Not set');
  console.log('NODE_ENV:', process.env.NODE_ENV || 'Not set');
  console.log('');
  
  if (!process.env.DATABASE_URL) {
    console.error('❌ DATABASE_URL is not set. Please check your .env.local file.');
    return;
  }
  
  // Test basic pg connection
  console.log('🔌 Testing basic PostgreSQL connection...');
  const { Pool } = require('pg');
  
  const pool = new Pool({
    connectionString: process.env.DATABASE_URL,
    connectionTimeoutMillis: 10000,
  });
  
  try {
    const client = await pool.connect();
    console.log('✅ Basic connection successful');
    
    // Test database version
    const versionResult = await client.query('SELECT version()');
    console.log('📊 Database version:', versionResult.rows[0].version.split(' ')[0]);
    
    // Check if table exists
    const tableCheck = await client.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name = 'newtable'
      );
    `);
    
    if (tableCheck.rows[0].exists) {
      console.log('✅ Table "newtable" exists');
      
      // Check table structure
      const structureResult = await client.query(`
        SELECT column_name, data_type, is_nullable 
        FROM information_schema.columns 
        WHERE table_name = 'newtable' 
        ORDER BY ordinal_position;
      `);
      
      console.log('📋 Table structure:');
      structureResult.rows.forEach(row => {
        console.log(`   ${row.column_name}: ${row.data_type} (${row.is_nullable === 'YES' ? 'nullable' : 'not null'})`);
      });
      
      // Test insert operation
      console.log('\n🧪 Testing insert operation...');
      const testName = 'DebugTest_' + Date.now();
      const testScore = 999;
      
      await client.query(
        'INSERT INTO public.newtable (playname, score) VALUES ($1, $2)',
        [testName, testScore]
      );
      console.log('✅ Insert successful');
      
      // Test select operation
      const selectResult = await client.query(
        'SELECT * FROM public.newtable WHERE playname = $1',
        [testName]
      );
      
      if (selectResult.rows.length > 0) {
        console.log('✅ Select successful:', selectResult.rows[0]);
        
        // Clean up
        await client.query(
          'DELETE FROM public.newtable WHERE playname = $1',
          [testName]
        );
        console.log('✅ Cleanup successful');
      } else {
        console.log('❌ Select failed - no rows returned');
      }
      
    } else {
      console.log('❌ Table "newtable" does not exist');
      console.log('💡 Run: npm run init-db to create the table');
    }
    
    client.release();
    
  } catch (error) {
    console.error('❌ Database error:', {
      message: error.message,
      code: error.code,
      detail: error.detail,
      hint: error.hint
    });
    
    if (error.code === 'ECONNREFUSED') {
      console.log('\n💡 Connection refused. Please check:');
      console.log('   - PostgreSQL is running');
      console.log('   - Connection details are correct');
      console.log('   - Firewall is not blocking the connection');
    } else if (error.code === '28P01') {
      console.log('\n💡 Authentication failed. Please check:');
      console.log('   - Username and password are correct');
      console.log('   - User has access to the database');
    } else if (error.code === '3D000') {
      console.log('\n💡 Database does not exist. Please create it:');
      console.log('   CREATE DATABASE minesweeper_db;');
    }
  } finally {
    await pool.end();
  }
  
  // Test our custom db module
  console.log('\n🔧 Testing custom database module...');
  try {
    // Use absolute path from project root
    const path = require('path');
    const dbPath = path.join(__dirname, '..', 'lib', 'db.ts');
    console.log('Attempting to load:', dbPath);

    // Since we're in a JS script, we need to use the compiled version or import differently
    // For now, let's skip this test and focus on the basic connection
    console.log('⏭️  Skipping module test (TypeScript compilation needed)');
    return;
    const testName = 'ModuleTest_' + Date.now();
    const testScore = 888;
    
    await savePlayerScore(testName, testScore);
    console.log('✅ Custom module test successful');
    
    // Clean up
    const cleanupPool = new Pool({
      connectionString: process.env.DATABASE_URL,
    });
    const cleanupClient = await cleanupPool.connect();
    await cleanupClient.query(
      'DELETE FROM public.newtable WHERE playname = $1',
      [testName]
    );
    cleanupClient.release();
    await cleanupPool.end();
    console.log('✅ Module test cleanup successful');
    
  } catch (error) {
    console.error('❌ Custom module error:', error.message);
  }
  
  console.log('\n🎉 Database debugging completed!');
}

debugDatabase().catch(console.error);
