#!/usr/bin/env node

/**
 * API testing script
 * Tests the save-score API endpoint with various inputs
 */

const testCases = [
  {
    name: 'Valid input',
    data: { name: 'TestPlayer', score: 100 },
    expectedStatus: 200
  },
  {
    name: 'Empty name',
    data: { name: '', score: 100 },
    expectedStatus: 400
  },
  {
    name: 'Invalid score (string)',
    data: { name: 'TestPlayer', score: 'invalid' },
    expectedStatus: 400
  },
  {
    name: 'Negative score',
    data: { name: 'TestPlayer', score: -10 },
    expectedStatus: 400
  },
  {
    name: 'Missing name',
    data: { score: 100 },
    expectedStatus: 400
  },
  {
    name: 'Missing score',
    data: { name: 'TestPlayer' },
    expectedStatus: 400
  }
];

async function testAPI() {
  const baseUrl = 'http://localhost:3000';
  
  console.log('🧪 Testing API endpoints...\n');
  
  for (const testCase of testCases) {
    console.log(`Testing: ${testCase.name}`);
    
    try {
      const response = await fetch(`${baseUrl}/api/save-score`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(testCase.data),
      });
      
      const data = await response.json();
      
      if (response.status === testCase.expectedStatus) {
        console.log(`✅ PASS - Status: ${response.status}`);
        if (data.success) {
          console.log(`   Message: ${data.message || 'Success'}`);
        } else {
          console.log(`   Error: ${data.error}`);
        }
      } else {
        console.log(`❌ FAIL - Expected: ${testCase.expectedStatus}, Got: ${response.status}`);
        console.log(`   Response: ${JSON.stringify(data)}`);
      }
    } catch (error) {
      console.log(`❌ ERROR - ${error.message}`);
    }
    
    console.log('');
  }
}

// Check if server is running
async function checkServer() {
  try {
    const response = await fetch('http://localhost:3000');
    if (response.ok) {
      console.log('✅ Server is running\n');
      return true;
    }
  } catch (error) {
    console.log('❌ Server is not running. Please start the development server first:');
    console.log('   npm run dev\n');
    return false;
  }
}

async function main() {
  const serverRunning = await checkServer();
  if (serverRunning) {
    await testAPI();
  }
}

main().catch(console.error);
