"use client";
import { useState, useEffect } from "react";

type CellState = {
  revealed: boolean;
  hasMine: boolean;
  flagged: boolean;
  adjacentMines: number;
};

export default function MinesweeperGame() {
  const [board, setBoard] = useState<CellState[][]>([]);
  const [boardSize, setBoardSize] = useState(10);
  const [mineCount, setMineCount] = useState(15);
  const [gameOver, setGameOver] = useState(false);
  const [gameWon, setGameWon] = useState(false);
  const [playerName, setPlayerName] = useState('');
  const [gameStarted, setGameStarted] = useState(false);

  // Initialize the board
  useEffect(() => {
    if (gameStarted) {
      initializeBoard();
    }
  }, [boardSize, mineCount, gameStarted]);

  const initializeBoard = () => {
    const newBoard: CellState[][] = Array(boardSize)
      .fill(null)
      .map(() =>
        Array(boardSize).fill(null).map(() => ({
          revealed: false,
          hasMine: false,
          flagged: false,
          adjacentMines: 0,
        }))
      );

    // Place mines randomly
    let minesPlaced = 0;
    while (minesPlaced < mineCount) {
      const x = Math.floor(Math.random() * boardSize);
      const y = Math.floor(Math.random() * boardSize);
      if (!newBoard[x][y].hasMine) {
        newBoard[x][y].hasMine = true;
        minesPlaced++;
      }
    }

    // Calculate adjacent mines
    for (let x = 0; x < boardSize; x++) {
      for (let y = 0; y < boardSize; y++) {
        if (!newBoard[x][y].hasMine) {
          let count = 0;
          for (let dx = -1; dx <= 1; dx++) {
            for (let dy = -1; dy <= 1; dy++) {
              const nx = x + dx;
              const ny = y + dy;
              if (
                nx >= 0 &&
                nx < boardSize &&
                ny >= 0 &&
                ny < boardSize &&
                newBoard[nx][ny].hasMine
              ) {
                count++;
              }
            }
          }
          newBoard[x][y].adjacentMines = count;
        }
      }
    }

    setBoard(newBoard);
    setGameOver(false);
    setGameWon(false);
  };

  const handleStartGame = () => {
    if (playerName.trim()) {
      setGameStarted(true);
    } else {
      alert('请输入玩家姓名！');
    }
  };

  const handleCellClick = (x: number, y: number) => {
    if (!gameStarted || gameOver || gameWon || board[x][y].flagged) return;

    const newBoard = [...board];
    
    if (newBoard[x][y].hasMine) {
      // Game over - reveal all mines
      for (let i = 0; i < boardSize; i++) {
        for (let j = 0; j < boardSize; j++) {
          if (newBoard[i][j].hasMine) {
            newBoard[i][j].revealed = true;
          }
        }
      }
      setBoard(newBoard);
      setGameOver(true);
      // Save score on game over (loss) - score could be time taken or number of revealed cells
      // For simplicity, let's save the number of revealed cells as a score for now
      const revealedCells = newBoard.reduce((count, row) => count + row.filter(cell => cell.revealed).length, 0);
      saveScore(playerName, revealedCells.toString());
      return;
    }

    // Reveal cell and adjacent cells if empty
    const revealCells = (x: number, y: number) => {
      if (
        x < 0 ||
        x >= boardSize ||
        y < 0 ||
        y >= boardSize ||
        newBoard[x][y].revealed ||
        newBoard[x][y].flagged
      ) {
        return;
      }

      newBoard[x][y].revealed = true;

      if (newBoard[x][y].adjacentMines === 0) {
        for (let dx = -1; dx <= 1; dx++) {
          for (let dy = -1; dy <= 1; dy++) {
            revealCells(x + dx, y + dy);
          }
        }
      }
    };

    revealCells(x, y);
    setBoard(newBoard);

    // Check if player has won
    checkWinCondition(newBoard);
  };

  const handleRightClick = (e: React.MouseEvent, x: number, y: number) => {
    e.preventDefault();
    if (gameOver || gameWon || board[x][y].revealed) return;

    const newBoard = [...board];
    newBoard[x][y].flagged = !newBoard[x][y].flagged;
    setBoard(newBoard);
  };

  const checkWinCondition = (currentBoard: CellState[][]) => {
    let unrevealedSafeCells = 0;
    for (let x = 0; x < boardSize; x++) {
      for (let y = 0; y < boardSize; y++) {
        if (!currentBoard[x][y].revealed && !currentBoard[x][y].hasMine) {
          unrevealedSafeCells++;
        }
      }
    }
    if (unrevealedSafeCells === 0) {
      setGameWon(true);
      // Save score on game over (win) - score could be time taken or number of revealed cells
      // For simplicity, let's save the number of revealed cells as a score for now
      const revealedCells = currentBoard.reduce((count, row) => count + row.filter(cell => cell.revealed).length, 0);
      saveScore(playerName, revealedCells.toString());
    }
  };

  const saveScore = async (name: string, score: string) => {
    try {
      const response = await fetch('/api/save-score', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ name, score }),
      });
      const data = await response.json();
      if (data.success) {
        console.log('Score saved successfully!');
      } else {
        console.error('Failed to save score:', data.error);
      }
    } catch (error) {
      console.error('Error saving score:', error);
    }
  };

  const resetGame = () => {
    // Keep gameStarted true and retain playerName
    initializeBoard(); // Initialize board for the next game start
  };

  const getCellColor = (cell: CellState) => {
    if (cell.revealed) {
      return cell.hasMine ? "bg-red-500" : "bg-gray-200";
    }
    return cell.flagged ? "bg-yellow-200" : "bg-gray-400";
  };

  return (
    <div className="min-h-screen flex flex-col items-center justify-center p-8">
      <h1 className="text-3xl font-bold mb-6">扫雷游戏</h1>

      {!gameStarted ? (
        <div className="mb-4 flex flex-col items-center">
          <label htmlFor="playerName" className="mb-2 text-lg">请输入玩家姓名:</label>
          <input
            type="text"
            id="playerName"
            value={playerName}
            onChange={(e) => setPlayerName(e.target.value)}
            className="p-2 border rounded mb-4 text-center"
            placeholder="玩家姓名"
          />
          <button
            onClick={handleStartGame}
            className="px-6 py-3 bg-green-500 text-white rounded hover:bg-green-600 text-lg"
          >
            开始游戏
          </button>
        </div>
      ) : (
        <>
          <div className="mb-4 text-xl font-semibold">玩家: {playerName}</div>
          <div className="mb-4 flex gap-4">
            <div>
              <label className="mr-2">棋盘大小:</label>
              <select
                value={boardSize}
                onChange={(e) => setBoardSize(Number(e.target.value))}
                className="p-2 border rounded"
              >
                <option value={8}>8x8</option>
                <option value={10}>10x10</option>
                <option value={12}>12x12</option>
              </select>
            </div>
            <div>
              <label className="mr-2">地雷数量:</label>
              <select
                value={mineCount}
                onChange={(e) => setMineCount(Number(e.target.value))}
                className="p-2 border rounded"
              >
                <option value={10}>10</option>
                <option value={15}>15</option>
                <option value={20}>20</option>
              </select>
            </div>
            <button
              onClick={resetGame}
              className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
            >
              重新开始
            </button>
          </div>

          {gameOver && (
            <div className="mb-4 text-red-600 font-bold">游戏结束！你踩到地雷了！</div>
          )}
          {gameWon && (
            <div className="mb-4 text-green-600 font-bold">恭喜你赢了！</div>
          )}

          <div
            className="grid gap-1"
            style={{
              gridTemplateColumns: `repeat(${boardSize}, 30px)`,
            }}
          >
            {board.map((row, x) =>
              row.map((cell, y) => (
                <div
                  key={`${x}-${y}`}
                  className={`w-8 h-8 flex items-center justify-center border border-gray-600 cursor-pointer ${getCellColor(cell)}`}
                  onClick={() => handleCellClick(x, y)}
                  onContextMenu={(e) => handleRightClick(e, x, y)}
                >
                  {cell.revealed && !cell.hasMine && cell.adjacentMines > 0 && (
                    <span className="font-bold">
                      {cell.adjacentMines}
                    </span>
                  )}
                  {cell.revealed && cell.hasMine && "💣"}
                  {!cell.revealed && cell.flagged && "🚩"}
                </div>
              ))
            )}
          </div>

          <div className="mt-6 text-sm text-gray-600">
            <p>左键点击：翻开格子</p>
            <p>右键点击：标记地雷</p>
          </div>
        </>
      )}
    </div>
  );
}
