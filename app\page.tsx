"use client";
import { useState, useEffect } from "react";

type CellState = {
  revealed: boolean;
  hasMine: boolean;
  flagged: boolean;
  adjacentMines: number;
};

export default function MinesweeperGame() {
  const [board, setBoard] = useState<CellState[][]>([]);
  const [boardSize, setBoardSize] = useState(10);
  const [mineCount, setMineCount] = useState(15);
  const [gameOver, setGameOver] = useState(false);
  const [gameWon, setGameWon] = useState(false);
  const [playerName, setPlayerName] = useState('');
  const [gameStarted, setGameStarted] = useState(false);
  const [saveMessage, setSaveMessage] = useState<string>('');
  const [showLeaderboard, setShowLeaderboard] = useState(false);
  const [leaderboardData, setLeaderboardData] = useState<{ playname: string; score: number }[]>([]);
  const [loadingLeaderboard, setLoadingLeaderboard] = useState(false);
  const [gameStartTime, setGameStartTime] = useState<number>(0);

  // Initialize the board
  useEffect(() => {
    if (gameStarted) {
      initializeBoard();
    }
  }, [boardSize, mineCount, gameStarted]);

  const initializeBoard = () => {
    const newBoard: CellState[][] = Array(boardSize)
      .fill(null)
      .map(() =>
        Array(boardSize).fill(null).map(() => ({
          revealed: false,
          hasMine: false,
          flagged: false,
          adjacentMines: 0,
        }))
      );

    // Place mines randomly
    let minesPlaced = 0;
    while (minesPlaced < mineCount) {
      const x = Math.floor(Math.random() * boardSize);
      const y = Math.floor(Math.random() * boardSize);
      if (!newBoard[x][y].hasMine) {
        newBoard[x][y].hasMine = true;
        minesPlaced++;
      }
    }

    // Calculate adjacent mines
    for (let x = 0; x < boardSize; x++) {
      for (let y = 0; y < boardSize; y++) {
        if (!newBoard[x][y].hasMine) {
          let count = 0;
          for (let dx = -1; dx <= 1; dx++) {
            for (let dy = -1; dy <= 1; dy++) {
              const nx = x + dx;
              const ny = y + dy;
              if (
                nx >= 0 &&
                nx < boardSize &&
                ny >= 0 &&
                ny < boardSize &&
                newBoard[nx][ny].hasMine
              ) {
                count++;
              }
            }
          }
          newBoard[x][y].adjacentMines = count;
        }
      }
    }

    setBoard(newBoard);
    setGameOver(false);
    setGameWon(false);
    setGameStartTime(Date.now()); // 记录游戏开始时间
  };

  const handleStartGame = () => {
    if (playerName.trim()) {
      setGameStarted(true);
    } else {
      alert('请输入玩家姓名！');
    }
  };

  const handleCellClick = (x: number, y: number) => {
    if (!gameStarted || gameOver || gameWon || board[x][y].flagged) return;

    const newBoard = [...board];
    
    if (newBoard[x][y].hasMine) {
      // Game over - reveal all mines
      for (let i = 0; i < boardSize; i++) {
        for (let j = 0; j < boardSize; j++) {
          if (newBoard[i][j].hasMine) {
            newBoard[i][j].revealed = true;
          }
        }
      }
      setBoard(newBoard);
      setGameOver(true);
      // 计算失败时的分数
      const gameTime = Date.now() - gameStartTime;
      const finalScore = calculateScore(false, gameTime);
      const gameTimeSeconds = Math.floor(gameTime / 1000);

      // 保存分数并显示排行榜
      saveScore(playerName, finalScore, boardSize, mineCount, false, gameTimeSeconds);

      // 游戏结束后自动显示排行榜并刷新数据
      setTimeout(() => {
        setShowLeaderboard(true);
        // 确保显示最新的排行榜数据
        setTimeout(() => {
          fetchLeaderboard();
        }, 200);
      }, 1000);
      return;
    }

    // Reveal cell and adjacent cells if empty
    const revealCells = (x: number, y: number) => {
      if (
        x < 0 ||
        x >= boardSize ||
        y < 0 ||
        y >= boardSize ||
        newBoard[x][y].revealed ||
        newBoard[x][y].flagged
      ) {
        return;
      }

      newBoard[x][y].revealed = true;

      if (newBoard[x][y].adjacentMines === 0) {
        for (let dx = -1; dx <= 1; dx++) {
          for (let dy = -1; dy <= 1; dy++) {
            revealCells(x + dx, y + dy);
          }
        }
      }
    };

    revealCells(x, y);
    setBoard(newBoard);

    // Check if player has won
    checkWinCondition(newBoard);
  };

  const handleRightClick = (e: React.MouseEvent, x: number, y: number) => {
    e.preventDefault();
    if (gameOver || gameWon || board[x][y].revealed) return;

    const newBoard = [...board];
    newBoard[x][y].flagged = !newBoard[x][y].flagged;
    setBoard(newBoard);
  };

  const checkWinCondition = (currentBoard: CellState[][]) => {
    let unrevealedSafeCells = 0;
    for (let x = 0; x < boardSize; x++) {
      for (let y = 0; y < boardSize; y++) {
        if (!currentBoard[x][y].revealed && !currentBoard[x][y].hasMine) {
          unrevealedSafeCells++;
        }
      }
    }
    if (unrevealedSafeCells === 0) {
      setGameWon(true);
      // 计算获胜时的分数
      const gameTime = Date.now() - gameStartTime;
      const finalScore = calculateScore(true, gameTime);
      const gameTimeSeconds = Math.floor(gameTime / 1000);

      // 保存分数并显示排行榜
      saveScore(playerName, finalScore, boardSize, mineCount, true, gameTimeSeconds);

      // 获胜后自动显示排行榜并刷新数据
      setTimeout(() => {
        setShowLeaderboard(true);
        // 确保显示最新的排行榜数据
        setTimeout(() => {
          fetchLeaderboard();
        }, 200);
      }, 1000);
    }
  };

  const saveScore = async (
    name: string,
    score: number,
    boardSize: number = 10,
    mineCount: number = 15,
    isWin: boolean = false,
    gameTimeSeconds: number = 0
  ) => {
    // Validate inputs before sending
    if (!name || typeof name !== 'string' || name.trim() === '') {
      console.error('Invalid player name');
      setSaveMessage('❌ 无效的玩家姓名');
      return;
    }

    if (typeof score !== 'number' || isNaN(score) || score < 0) {
      console.error('Invalid score value');
      setSaveMessage('❌ 无效的分数值');
      return;
    }

    setSaveMessage('💾 正在保存分数...');

    try {
      const response = await fetch('/api/save-score', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: name.trim(),
          score,
          boardSize,
          mineCount,
          isWin,
          gameTimeSeconds
        }),
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`HTTP ${response.status}: ${errorText}`);
      }

      const data = await response.json();
      if (data.success) {
        console.log('✅ Score saved successfully!', {
          name: name.trim(),
          score,
          boardSize,
          mineCount,
          isWin,
          gameTimeSeconds
        });
        setSaveMessage('✅ 分数已保存到排行榜！');

        // Always refresh leaderboard after successful save to show updated rankings
        console.log('🔄 Refreshing leaderboard after score save...');
        setTimeout(() => {
          fetchLeaderboard();
        }, 800); // Slightly longer delay to ensure database has been updated

        // Clear message after 5 seconds (longer to let user see the success)
        setTimeout(() => setSaveMessage(''), 5000);
      } else {
        console.error('❌ Failed to save score:', data.error || 'Unknown error');
        const errorMsg = data.error || '未知错误';
        if (errorMsg.includes('Database connection failed') || errorMsg.includes('ECONNREFUSED')) {
          setSaveMessage('⚠️ 数据库未连接，分数未保存到排行榜（开发模式）');
        } else if (errorMsg.includes('does not exist')) {
          setSaveMessage('❌ 数据库表不存在，请运行: npm run init-db');
        } else {
          setSaveMessage(`❌ 保存到排行榜失败: ${errorMsg}`);
        }

        // Clear error message after 8 seconds
        setTimeout(() => setSaveMessage(''), 8000);
      }
    } catch (error) {
      console.error('❌ Error saving score:', error);
      setSaveMessage(`❌ 保存错误: ${error instanceof Error ? error.message : '网络错误'}`);
    }
  };

  const fetchLeaderboard = async () => {
    setLoadingLeaderboard(true);
    try {
      const response = await fetch('/api/leaderboard?limit=10');
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      if (data.success) {
        console.log('📊 Leaderboard data fetched:', {
          currentPlayer: playerName,
          leaderboardCount: data.data?.length || 0,
          leaderboardData: data.data?.map((entry: any) => ({
            name: entry.playname,
            score: entry.score,
            trimmedName: entry.playname?.trim(),
            matchesCurrentPlayer: entry.playname?.trim().toLowerCase() === playerName?.trim().toLowerCase()
          }))
        });
        setLeaderboardData(data.data || []);
      } else {
        console.error('Failed to fetch leaderboard:', data.error);
        setLeaderboardData([]);
      }
    } catch (error) {
      console.error('Error fetching leaderboard:', error);
      setLeaderboardData([]);
    } finally {
      setLoadingLeaderboard(false);
    }
  };

  const toggleLeaderboard = () => {
    if (!showLeaderboard) {
      fetchLeaderboard();
    }
    setShowLeaderboard(!showLeaderboard);
  };

  const testSaveScore = async () => {
    if (!playerName.trim()) {
      setSaveMessage('❌ 请先输入玩家姓名');
      return;
    }

    const testScore = Math.floor(Math.random() * 300) + 100; // 100-400 分
    const testBoardSize = [8, 10, 12][Math.floor(Math.random() * 3)];
    const testMineCount = testBoardSize === 8 ? 10 : testBoardSize === 10 ? 15 : 20;
    const testIsWin = Math.random() > 0.5;
    const testGameTime = Math.floor(Math.random() * 300) + 30; // 30-330 秒

    setSaveMessage('🧪 测试保存分数到排行榜...');
    await saveScore(playerName, testScore, testBoardSize, testMineCount, testIsWin, testGameTime);
  };

  const calculateScore = (isWin: boolean, gameTimeMs: number): number => {
    if (!isWin) {
      // 失败时给予少量分数，基于翻开的格子数
      const revealedCells = board.reduce((count, row) => count + row.filter(cell => cell.revealed && !cell.hasMine).length, 0);
      return Math.max(1, revealedCells);
    }

    // 获胜时的分数计算
    const gameTimeSeconds = Math.max(1, Math.floor(gameTimeMs / 1000));

    // 基础分数：根据难度
    let baseScore = 0;
    if (boardSize === 8) baseScore = 100;      // 简单
    else if (boardSize === 10) baseScore = 200; // 中等
    else if (boardSize === 12) baseScore = 300; // 困难

    // 地雷数量奖励
    const mineBonus = mineCount * 5;

    // 时间奖励（越快完成分数越高）
    const timeBonus = Math.max(0, Math.floor(1000 / gameTimeSeconds));

    // 完美奖励（没有错误标记）
    const wrongFlags = board.reduce((count, row) =>
      count + row.filter(cell => cell.flagged && !cell.hasMine).length, 0
    );
    const perfectBonus = wrongFlags === 0 ? 50 : 0;

    return baseScore + mineBonus + timeBonus + perfectBonus;
  };

  const resetGame = () => {
    // Keep gameStarted true and retain playerName
    setSaveMessage(''); // Clear any save messages
    initializeBoard(); // Initialize board for the next game start
  };

  const backToMenu = () => {
    setGameStarted(false);
    setPlayerName('');
    setSaveMessage('');
    setBoard([]);
    setGameOver(false);
    setGameWon(false);
  };

  const getCellColor = (cell: CellState) => {
    if (cell.revealed) {
      if (cell.hasMine) {
        return "bg-red-500 text-white shadow-lg"; // 地雷：红色背景
      } else {
        return "bg-white border-gray-300 shadow-sm"; // 已翻开的安全格子：白色背景
      }
    } else {
      if (cell.flagged) {
        return "bg-yellow-300 border-yellow-400 shadow-md"; // 已标记：黄色背景
      } else {
        return "bg-gray-300 border-gray-400 hover:bg-gray-200 shadow-sm"; // 未翻开：灰色背景
      }
    }
  };

  return (
    <div className="min-h-screen flex flex-col items-center justify-center p-8 bg-gradient-to-br from-blue-50 to-indigo-100">
      <div className="bg-white rounded-lg shadow-lg p-8 max-w-4xl w-full">
        <h1 className="text-4xl font-bold mb-2 text-center text-gray-800">🎮 扫雷游戏</h1>
        <p className="text-center text-gray-600 mb-8">经典益智游戏，挑战你的逻辑思维</p>

        {!gameStarted ? (
          <div className="mb-4 flex flex-col items-center">
            <div className="bg-gray-50 rounded-lg p-6 mb-6">
              <h2 className="text-xl font-semibold mb-4 text-center">游戏说明</h2>
              <ul className="text-sm text-gray-700 space-y-2">
                <li>• 左键点击翻开格子</li>
                <li>• 右键点击标记可能的地雷</li>
                <li>• 数字表示周围地雷的数量</li>
                <li>• 翻开所有非地雷格子即可获胜</li>
              </ul>

              <div className="mt-4 p-3 bg-blue-50 rounded border border-blue-200">
                <h3 className="text-sm font-semibold text-blue-800 mb-2">🏆 分数计算</h3>
                <ul className="text-xs text-blue-700 space-y-1">
                  <li>• 基础分数：简单100分，中等200分，困难300分</li>
                  <li>• 地雷奖励：每个地雷+5分</li>
                  <li>• 速度奖励：完成越快分数越高</li>
                  <li>• 完美奖励：无错误标记+50分</li>
                </ul>
              </div>
            </div>

            <label htmlFor="playerName" className="mb-2 text-lg font-medium text-gray-700">
              请输入您的姓名：
            </label>
            <input
              type="text"
              id="playerName"
              value={playerName}
              onChange={(e) => setPlayerName(e.target.value)}
              className="p-3 border-2 border-gray-300 rounded-lg mb-4 text-center focus:border-blue-500 focus:outline-none"
              placeholder="输入您的姓名"
              maxLength={20}
            />
            <div className="flex flex-wrap gap-4 justify-center">
              <button
                onClick={handleStartGame}
                className="px-8 py-3 bg-gradient-to-r from-green-500 to-green-600 text-white rounded-lg hover:from-green-600 hover:to-green-700 text-lg font-medium shadow-md transition-all duration-200"
              >
                🚀 开始游戏
              </button>
              <button
                onClick={toggleLeaderboard}
                className="px-6 py-3 bg-gradient-to-r from-purple-500 to-purple-600 text-white rounded-lg hover:from-purple-600 hover:to-purple-700 text-lg font-medium shadow-md transition-all duration-200"
              >
                🏆 排行榜
              </button>
              <button
                onClick={testSaveScore}
                className="px-4 py-3 bg-gradient-to-r from-orange-500 to-orange-600 text-white rounded-lg hover:from-orange-600 hover:to-orange-700 text-sm font-medium shadow-md transition-all duration-200"
                title="测试分数保存功能"
              >
                🧪 测试保存
              </button>
            </div>

            {/* 排行榜显示 */}
            {showLeaderboard && (
              <div className="mt-8 bg-gray-50 rounded-lg p-6">
                <div className="flex items-center justify-between mb-4">
                  <div>
                    <h2 className="text-xl font-semibold text-gray-800">🏆 排行榜</h2>
                    {playerName && (
                      <p className="text-sm text-gray-600">
                        当前玩家：<span className="font-medium text-blue-600">{playerName}</span>
                      </p>
                    )}
                  </div>
                  <button
                    onClick={() => setShowLeaderboard(false)}
                    className="text-gray-500 hover:text-gray-700 text-xl"
                  >
                    ✕
                  </button>
                </div>

                {loadingLeaderboard ? (
                  <div className="text-center py-8">
                    <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600"></div>
                    <p className="mt-2 text-gray-600">加载中...</p>
                  </div>
                ) : leaderboardData.length > 0 ? (
                  <div className="space-y-2">
                    <div className="grid grid-cols-4 gap-3 text-sm font-semibold text-gray-700 border-b pb-2">
                      <span>排名</span>
                      <span>玩家</span>
                      <span className="text-center">难度</span>
                      <span className="text-right">分数</span>
                    </div>
                    {leaderboardData.map((entry, index) => {
                      // 从数据库获取的数据可能没有新字段，提供默认值
                      const boardSize = (entry as any).board_size || 10;
                      const mineCount = (entry as any).mine_count || 15;
                      const isWin = (entry as any).is_win || false;
                      const gameTime = (entry as any).game_time_seconds || 0;

                      // 改进用户名比较逻辑
                      const isCurrentPlayer = entry.playname?.trim().toLowerCase() === playerName?.trim().toLowerCase();

                      const difficultyText = boardSize === 8 ? '简单' :
                                           boardSize === 10 ? '中等' :
                                           boardSize === 12 ? '困难' : `${boardSize}×${boardSize}`;

                      return (
                        <div
                          key={`${entry.playname}-${index}-${entry.score}`}
                          className={`grid grid-cols-4 gap-3 text-sm py-2 px-3 rounded ${
                            isCurrentPlayer ? 'bg-gradient-to-r from-blue-100 to-blue-50 border border-blue-300 ring-1 ring-blue-400' :
                            index < 3 ? 'bg-gradient-to-r from-yellow-100 to-yellow-50' : 'bg-white'
                          }`}
                        >
                          <span className="flex items-center gap-2">
                            {index === 0 && '🥇'}
                            {index === 1 && '🥈'}
                            {index === 2 && '🥉'}
                            {index > 2 && `${index + 1}.`}
                          </span>
                          <div className={`font-medium ${isCurrentPlayer ? 'text-blue-800' : 'text-gray-800'}`}>
                            <div className="truncate flex items-center gap-1">
                              {entry.playname}
                              {isCurrentPlayer && <span className="text-blue-600">👤</span>}
                            </div>
                            {gameTime > 0 && (
                              <div className={`text-xs ${isCurrentPlayer ? 'text-blue-600' : 'text-gray-500'}`}>
                                {Math.floor(gameTime / 60)}:{(gameTime % 60).toString().padStart(2, '0')}
                                {isWin && ' 🏆'}
                              </div>
                            )}
                          </div>
                          <span className="text-center text-xs">
                            <div>{difficultyText}</div>
                            {mineCount > 0 && <div className="text-gray-500">{mineCount}💣</div>}
                          </span>
                          <span className={`text-right font-bold ${
                            isCurrentPlayer ? 'text-blue-600' : 'text-purple-600'
                          }`}>
                            {entry.score}
                          </span>
                        </div>
                      );
                    })}
                  </div>
                ) : (
                  <div className="text-center py-8 text-gray-500">
                    <p className="text-lg">🎯</p>
                    <p>暂无排行榜数据</p>
                    <p className="text-sm mt-1">开始游戏来创建第一个记录吧！</p>
                  </div>
                )}

                <div className="mt-4 text-center">
                  <button
                    onClick={fetchLeaderboard}
                    className="px-4 py-2 bg-purple-100 text-purple-700 rounded-lg hover:bg-purple-200 transition-colors duration-200"
                  >
                    🔄 刷新排行榜
                  </button>
                </div>
              </div>
            )}
          </div>
      ) : (
        <>
          <div className="mb-6 text-center">
            <div className="text-2xl font-bold text-gray-800 mb-2">
              👤 玩家：{playerName}
            </div>
            <div className="text-sm text-gray-600">
              祝您游戏愉快！
            </div>
          </div>

          <div className="mb-6 flex flex-wrap gap-4 justify-center items-center bg-gray-50 p-4 rounded-lg">
            <div className="flex items-center gap-2">
              <label className="text-sm font-medium text-gray-700">🎯 棋盘大小：</label>
              <select
                value={boardSize}
                onChange={(e) => setBoardSize(Number(e.target.value))}
                className="p-2 border-2 border-gray-300 rounded-lg focus:border-blue-500 focus:outline-none"
              >
                <option value={8}>8×8 (简单)</option>
                <option value={10}>10×10 (中等)</option>
                <option value={12}>12×12 (困难)</option>
              </select>
            </div>
            <div className="flex items-center gap-2">
              <label className="text-sm font-medium text-gray-700">💣 地雷数量：</label>
              <select
                value={mineCount}
                onChange={(e) => setMineCount(Number(e.target.value))}
                className="p-2 border-2 border-gray-300 rounded-lg focus:border-blue-500 focus:outline-none"
              >
                <option value={10}>10 个</option>
                <option value={15}>15 个</option>
                <option value={20}>20 个</option>
              </select>
            </div>
            <button
              onClick={resetGame}
              className="px-4 py-2 bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-lg hover:from-blue-600 hover:to-blue-700 font-medium shadow-md transition-all duration-200"
            >
              🔄 重新开始
            </button>
            <button
              onClick={backToMenu}
              className="px-4 py-2 bg-gradient-to-r from-gray-500 to-gray-600 text-white rounded-lg hover:from-gray-600 hover:to-gray-700 font-medium shadow-md transition-all duration-200"
            >
              🏠 返回主菜单
            </button>
            <button
              onClick={toggleLeaderboard}
              className="px-4 py-2 bg-gradient-to-r from-purple-500 to-purple-600 text-white rounded-lg hover:from-purple-600 hover:to-purple-700 font-medium shadow-md transition-all duration-200"
            >
              🏆 排行榜
            </button>
          </div>

          {/* 排行榜显示（游戏中） */}
          {showLeaderboard && (
            <div className="mb-6 bg-gray-50 rounded-lg p-4">
              <div className="flex items-center justify-between mb-3">
                <div>
                  <h3 className="text-lg font-semibold text-gray-800">🏆 排行榜</h3>
                  {playerName && (
                    <p className="text-xs text-gray-600">
                      玩家：<span className="font-medium text-blue-600">{playerName}</span>
                    </p>
                  )}
                </div>
                <button
                  onClick={() => setShowLeaderboard(false)}
                  className="text-gray-500 hover:text-gray-700"
                >
                  ✕
                </button>
              </div>

              {loadingLeaderboard ? (
                <div className="text-center py-4">
                  <div className="inline-block animate-spin rounded-full h-6 w-6 border-b-2 border-purple-600"></div>
                  <p className="mt-1 text-sm text-gray-600">加载中...</p>
                </div>
              ) : leaderboardData.length > 0 ? (
                <div className="space-y-1">
                  <div className="grid grid-cols-3 gap-2 text-xs font-semibold text-gray-700 border-b pb-1">
                    <span>排名</span>
                    <span>玩家</span>
                    <span className="text-right">分数</span>
                  </div>
                  {leaderboardData.slice(0, 5).map((entry, index) => {
                    // 改进用户名比较逻辑，忽略大小写和前后空格
                    const isCurrentPlayer = entry.playname?.trim().toLowerCase() === playerName?.trim().toLowerCase();

                    return (
                      <div
                        key={`${entry.playname}-${index}-${entry.score}`}
                        className={`grid grid-cols-3 gap-2 text-xs py-1 px-2 rounded ${
                          isCurrentPlayer ? 'bg-blue-100 border border-blue-300 ring-1 ring-blue-400' :
                          index < 3 ? 'bg-yellow-50' : 'bg-white'
                        }`}
                      >
                        <span className="flex items-center gap-1">
                          {index === 0 && '🥇'}
                          {index === 1 && '🥈'}
                          {index === 2 && '🥉'}
                          {index > 2 && `${index + 1}.`}
                        </span>
                        <span className={`font-medium truncate ${
                          isCurrentPlayer ? 'text-blue-800 font-bold' : 'text-gray-800'
                        }`}>
                          {entry.playname}
                          {isCurrentPlayer && ' 👤'}
                        </span>
                        <span className={`text-right font-bold ${
                          isCurrentPlayer ? 'text-blue-600' : 'text-purple-600'
                        }`}>
                          {entry.score}
                        </span>
                      </div>
                    );
                  })}
                </div>
              ) : (
                <div className="text-center py-4 text-gray-500">
                  <p className="text-sm">暂无排行榜数据</p>
                </div>
              )}
            </div>
          )}

          {/* 游戏状态消息 */}
          <div className="mb-6 text-center">
            {gameOver && (
              <div className="mb-4 p-4 bg-red-100 border border-red-300 rounded-lg">
                <div className="text-red-700 font-bold text-lg">💥 游戏结束！</div>
                <div className="text-red-600 text-sm mt-1">很遗憾，您踩到了地雷！再试一次吧！</div>
                <div className="text-red-500 text-xs mt-2">
                  您的分数已保存到排行榜 📊
                </div>
              </div>
            )}
            {gameWon && (
              <div className="mb-4 p-4 bg-green-100 border border-green-300 rounded-lg">
                <div className="text-green-700 font-bold text-lg">🎉 恭喜获胜！</div>
                <div className="text-green-600 text-sm mt-1">您成功找到了所有地雷！</div>
                <div className="text-green-500 text-xs mt-2">
                  高分已保存到排行榜 🏆 查看您的排名吧！
                </div>
              </div>
            )}
            {saveMessage && (
              <div className="mb-4 p-3 bg-blue-100 border border-blue-300 rounded-lg">
                <div className="text-blue-700 font-medium">
                  {saveMessage}
                </div>
              </div>
            )}
          </div>

          {/* 游戏统计信息 */}
          <div className="mb-4 flex justify-center gap-6 text-sm">
            <div className="flex items-center gap-1">
              <span className="text-gray-600">棋盘：</span>
              <span className="font-semibold">{boardSize}×{boardSize}</span>
            </div>
            <div className="flex items-center gap-1">
              <span className="text-gray-600">地雷：</span>
              <span className="font-semibold text-red-600">{mineCount} 个</span>
            </div>
            <div className="flex items-center gap-1">
              <span className="text-gray-600">剩余：</span>
              <span className="font-semibold text-blue-600">
                {board.length > 0 ? (boardSize * boardSize - mineCount - board.reduce((count, row) => count + row.filter(cell => cell.revealed && !cell.hasMine).length, 0)) : 0} 格
              </span>
            </div>
          </div>

          {/* 游戏板 */}
          <div className="flex justify-center mb-6">
            <div
              className="grid gap-1 p-4 bg-gray-100 rounded-lg shadow-inner"
              style={{
                gridTemplateColumns: `repeat(${boardSize}, 32px)`,
              }}
            >
              {board.map((row, x) =>
                row.map((cell, y) => (
                  <div
                    key={`${x}-${y}`}
                    className={`w-8 h-8 flex items-center justify-center border-2 cursor-pointer transition-all duration-150 hover:scale-105 ${getCellColor(cell)} ${
                      cell.revealed ? 'border-gray-400' : 'border-gray-500 hover:border-gray-600'
                    }`}
                    onClick={() => handleCellClick(x, y)}
                    onContextMenu={(e) => handleRightClick(e, x, y)}
                    title={cell.flagged ? '已标记为地雷' : cell.revealed ? (cell.hasMine ? '地雷！' : `周围有 ${cell.adjacentMines} 个地雷`) : '点击翻开'}
                  >
                    {cell.revealed && !cell.hasMine && cell.adjacentMines > 0 && (
                      <span className={`font-bold text-sm ${
                        cell.adjacentMines === 1 ? 'text-blue-600' :
                        cell.adjacentMines === 2 ? 'text-green-600' :
                        cell.adjacentMines === 3 ? 'text-red-600' :
                        cell.adjacentMines === 4 ? 'text-purple-600' :
                        cell.adjacentMines === 5 ? 'text-yellow-600' :
                        cell.adjacentMines === 6 ? 'text-pink-600' :
                        cell.adjacentMines === 7 ? 'text-black' : 'text-gray-600'
                      }`}>
                        {cell.adjacentMines}
                      </span>
                    )}
                    {cell.revealed && cell.hasMine && "💣"}
                    {!cell.revealed && cell.flagged && "🚩"}
                  </div>
                ))
              )}
            </div>
          </div>

          {/* 操作说明 */}
          <div className="mt-6 p-4 bg-gray-50 rounded-lg">
            <h3 className="text-sm font-semibold text-gray-700 mb-2 text-center">🎮 操作说明</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-xs text-gray-600">
              <div className="flex items-center gap-2">
                <span className="w-2 h-2 bg-blue-500 rounded-full"></span>
                <span>左键点击：翻开格子</span>
              </div>
              <div className="flex items-center gap-2">
                <span className="w-2 h-2 bg-red-500 rounded-full"></span>
                <span>右键点击：标记/取消标记地雷</span>
              </div>
              <div className="flex items-center gap-2">
                <span className="w-2 h-2 bg-green-500 rounded-full"></span>
                <span>数字：表示周围地雷数量</span>
              </div>
              <div className="flex items-center gap-2">
                <span className="w-2 h-2 bg-yellow-500 rounded-full"></span>
                <span>🚩：已标记的可疑地雷</span>
              </div>
            </div>
          </div>
        </>
      )}
      </div>
    </div>
  );
}
