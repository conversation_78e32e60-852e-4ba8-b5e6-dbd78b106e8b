"use client";
import { useState, useEffect } from "react";

type CellState = {
  revealed: boolean;
  hasMine: boolean;
  flagged: boolean;
  adjacentMines: number;
};

export default function MinesweeperGame() {
  const [board, setBoard] = useState<CellState[][]>([]);
  const [boardSize, setBoardSize] = useState(10);
  const [mineCount, setMineCount] = useState(15);
  const [gameOver, setGameOver] = useState(false);
  const [gameWon, setGameWon] = useState(false);
  const [playerName, setPlayerName] = useState('');
  const [gameStarted, setGameStarted] = useState(false);
  const [saveMessage, setSaveMessage] = useState<string>('');

  // Initialize the board
  useEffect(() => {
    if (gameStarted) {
      initializeBoard();
    }
  }, [boardSize, mineCount, gameStarted]);

  const initializeBoard = () => {
    const newBoard: CellState[][] = Array(boardSize)
      .fill(null)
      .map(() =>
        Array(boardSize).fill(null).map(() => ({
          revealed: false,
          hasMine: false,
          flagged: false,
          adjacentMines: 0,
        }))
      );

    // Place mines randomly
    let minesPlaced = 0;
    while (minesPlaced < mineCount) {
      const x = Math.floor(Math.random() * boardSize);
      const y = Math.floor(Math.random() * boardSize);
      if (!newBoard[x][y].hasMine) {
        newBoard[x][y].hasMine = true;
        minesPlaced++;
      }
    }

    // Calculate adjacent mines
    for (let x = 0; x < boardSize; x++) {
      for (let y = 0; y < boardSize; y++) {
        if (!newBoard[x][y].hasMine) {
          let count = 0;
          for (let dx = -1; dx <= 1; dx++) {
            for (let dy = -1; dy <= 1; dy++) {
              const nx = x + dx;
              const ny = y + dy;
              if (
                nx >= 0 &&
                nx < boardSize &&
                ny >= 0 &&
                ny < boardSize &&
                newBoard[nx][ny].hasMine
              ) {
                count++;
              }
            }
          }
          newBoard[x][y].adjacentMines = count;
        }
      }
    }

    setBoard(newBoard);
    setGameOver(false);
    setGameWon(false);
  };

  const handleStartGame = () => {
    if (playerName.trim()) {
      setGameStarted(true);
    } else {
      alert('请输入玩家姓名！');
    }
  };

  const handleCellClick = (x: number, y: number) => {
    if (!gameStarted || gameOver || gameWon || board[x][y].flagged) return;

    const newBoard = [...board];
    
    if (newBoard[x][y].hasMine) {
      // Game over - reveal all mines
      for (let i = 0; i < boardSize; i++) {
        for (let j = 0; j < boardSize; j++) {
          if (newBoard[i][j].hasMine) {
            newBoard[i][j].revealed = true;
          }
        }
      }
      setBoard(newBoard);
      setGameOver(true);
      // Save score on game over (loss) - score could be time taken or number of revealed cells
      // For simplicity, let's save the number of revealed cells as a score for now
      const revealedCells = newBoard.reduce((count, row) => count + row.filter(cell => cell.revealed).length, 0);
      saveScore(playerName, revealedCells);
      return;
    }

    // Reveal cell and adjacent cells if empty
    const revealCells = (x: number, y: number) => {
      if (
        x < 0 ||
        x >= boardSize ||
        y < 0 ||
        y >= boardSize ||
        newBoard[x][y].revealed ||
        newBoard[x][y].flagged
      ) {
        return;
      }

      newBoard[x][y].revealed = true;

      if (newBoard[x][y].adjacentMines === 0) {
        for (let dx = -1; dx <= 1; dx++) {
          for (let dy = -1; dy <= 1; dy++) {
            revealCells(x + dx, y + dy);
          }
        }
      }
    };

    revealCells(x, y);
    setBoard(newBoard);

    // Check if player has won
    checkWinCondition(newBoard);
  };

  const handleRightClick = (e: React.MouseEvent, x: number, y: number) => {
    e.preventDefault();
    if (gameOver || gameWon || board[x][y].revealed) return;

    const newBoard = [...board];
    newBoard[x][y].flagged = !newBoard[x][y].flagged;
    setBoard(newBoard);
  };

  const checkWinCondition = (currentBoard: CellState[][]) => {
    let unrevealedSafeCells = 0;
    for (let x = 0; x < boardSize; x++) {
      for (let y = 0; y < boardSize; y++) {
        if (!currentBoard[x][y].revealed && !currentBoard[x][y].hasMine) {
          unrevealedSafeCells++;
        }
      }
    }
    if (unrevealedSafeCells === 0) {
      setGameWon(true);
      // Save score on game over (win) - score could be time taken or number of revealed cells
      // For simplicity, let's save the number of revealed cells as a score for now
      const revealedCells = currentBoard.reduce((count, row) => count + row.filter(cell => cell.revealed).length, 0);
      saveScore(playerName, revealedCells);
    }
  };

  const saveScore = async (name: string, score: number) => {
    // Validate inputs before sending
    if (!name || typeof name !== 'string' || name.trim() === '') {
      console.error('Invalid player name');
      setSaveMessage('❌ 无效的玩家姓名');
      return;
    }

    if (typeof score !== 'number' || isNaN(score) || score < 0) {
      console.error('Invalid score value');
      setSaveMessage('❌ 无效的分数值');
      return;
    }

    setSaveMessage('💾 正在保存分数...');

    try {
      const response = await fetch('/api/save-score', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ name: name.trim(), score }),
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`HTTP ${response.status}: ${errorText}`);
      }

      const data = await response.json();
      if (data.success) {
        console.log('✅ Score saved successfully!', { name: name.trim(), score });
        setSaveMessage('✅ 分数保存成功！');
        // Clear message after 3 seconds
        setTimeout(() => setSaveMessage(''), 3000);
      } else {
        console.error('❌ Failed to save score:', data.error || 'Unknown error');
        const errorMsg = data.error || '未知错误';
        if (errorMsg.includes('Database connection failed') || errorMsg.includes('ECONNREFUSED')) {
          setSaveMessage('⚠️ 数据库未连接，分数未保存（开发模式）');
        } else {
          setSaveMessage(`❌ 保存失败: ${errorMsg}`);
        }
      }
    } catch (error) {
      console.error('❌ Error saving score:', error);
      setSaveMessage(`❌ 保存错误: ${error instanceof Error ? error.message : '网络错误'}`);
    }
  };

  const resetGame = () => {
    // Keep gameStarted true and retain playerName
    setSaveMessage(''); // Clear any save messages
    initializeBoard(); // Initialize board for the next game start
  };

  const backToMenu = () => {
    setGameStarted(false);
    setPlayerName('');
    setSaveMessage('');
    setBoard([]);
    setGameOver(false);
    setGameWon(false);
  };

  const getCellColor = (cell: CellState) => {
    if (cell.revealed) {
      if (cell.hasMine) {
        return "bg-red-500 text-white shadow-lg"; // 地雷：红色背景
      } else {
        return "bg-white border-gray-300 shadow-sm"; // 已翻开的安全格子：白色背景
      }
    } else {
      if (cell.flagged) {
        return "bg-yellow-300 border-yellow-400 shadow-md"; // 已标记：黄色背景
      } else {
        return "bg-gray-300 border-gray-400 hover:bg-gray-200 shadow-sm"; // 未翻开：灰色背景
      }
    }
  };

  return (
    <div className="min-h-screen flex flex-col items-center justify-center p-8 bg-gradient-to-br from-blue-50 to-indigo-100">
      <div className="bg-white rounded-lg shadow-lg p-8 max-w-4xl w-full">
        <h1 className="text-4xl font-bold mb-2 text-center text-gray-800">🎮 扫雷游戏</h1>
        <p className="text-center text-gray-600 mb-8">经典益智游戏，挑战你的逻辑思维</p>

        {!gameStarted ? (
          <div className="mb-4 flex flex-col items-center">
            <div className="bg-gray-50 rounded-lg p-6 mb-6">
              <h2 className="text-xl font-semibold mb-4 text-center">游戏说明</h2>
              <ul className="text-sm text-gray-700 space-y-2">
                <li>• 左键点击翻开格子</li>
                <li>• 右键点击标记可能的地雷</li>
                <li>• 数字表示周围地雷的数量</li>
                <li>• 翻开所有非地雷格子即可获胜</li>
              </ul>
            </div>

            <label htmlFor="playerName" className="mb-2 text-lg font-medium text-gray-700">
              请输入您的姓名：
            </label>
            <input
              type="text"
              id="playerName"
              value={playerName}
              onChange={(e) => setPlayerName(e.target.value)}
              className="p-3 border-2 border-gray-300 rounded-lg mb-4 text-center focus:border-blue-500 focus:outline-none"
              placeholder="输入您的姓名"
              maxLength={20}
            />
            <button
              onClick={handleStartGame}
              className="px-8 py-3 bg-gradient-to-r from-green-500 to-green-600 text-white rounded-lg hover:from-green-600 hover:to-green-700 text-lg font-medium shadow-md transition-all duration-200"
            >
              🚀 开始游戏
            </button>
          </div>
      ) : (
        <>
          <div className="mb-6 text-center">
            <div className="text-2xl font-bold text-gray-800 mb-2">
              👤 玩家：{playerName}
            </div>
            <div className="text-sm text-gray-600">
              祝您游戏愉快！
            </div>
          </div>

          <div className="mb-6 flex flex-wrap gap-4 justify-center items-center bg-gray-50 p-4 rounded-lg">
            <div className="flex items-center gap-2">
              <label className="text-sm font-medium text-gray-700">🎯 棋盘大小：</label>
              <select
                value={boardSize}
                onChange={(e) => setBoardSize(Number(e.target.value))}
                className="p-2 border-2 border-gray-300 rounded-lg focus:border-blue-500 focus:outline-none"
              >
                <option value={8}>8×8 (简单)</option>
                <option value={10}>10×10 (中等)</option>
                <option value={12}>12×12 (困难)</option>
              </select>
            </div>
            <div className="flex items-center gap-2">
              <label className="text-sm font-medium text-gray-700">💣 地雷数量：</label>
              <select
                value={mineCount}
                onChange={(e) => setMineCount(Number(e.target.value))}
                className="p-2 border-2 border-gray-300 rounded-lg focus:border-blue-500 focus:outline-none"
              >
                <option value={10}>10 个</option>
                <option value={15}>15 个</option>
                <option value={20}>20 个</option>
              </select>
            </div>
            <button
              onClick={resetGame}
              className="px-4 py-2 bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-lg hover:from-blue-600 hover:to-blue-700 font-medium shadow-md transition-all duration-200"
            >
              🔄 重新开始
            </button>
            <button
              onClick={backToMenu}
              className="px-4 py-2 bg-gradient-to-r from-gray-500 to-gray-600 text-white rounded-lg hover:from-gray-600 hover:to-gray-700 font-medium shadow-md transition-all duration-200"
            >
              🏠 返回主菜单
            </button>
          </div>

          {/* 游戏状态消息 */}
          <div className="mb-6 text-center">
            {gameOver && (
              <div className="mb-4 p-4 bg-red-100 border border-red-300 rounded-lg">
                <div className="text-red-700 font-bold text-lg">💥 游戏结束！</div>
                <div className="text-red-600 text-sm mt-1">很遗憾，您踩到了地雷！再试一次吧！</div>
              </div>
            )}
            {gameWon && (
              <div className="mb-4 p-4 bg-green-100 border border-green-300 rounded-lg">
                <div className="text-green-700 font-bold text-lg">🎉 恭喜获胜！</div>
                <div className="text-green-600 text-sm mt-1">您成功找到了所有地雷！</div>
              </div>
            )}
            {saveMessage && (
              <div className="mb-4 p-3 bg-blue-100 border border-blue-300 rounded-lg">
                <div className="text-blue-700 font-medium">
                  {saveMessage}
                </div>
              </div>
            )}
          </div>

          {/* 游戏统计信息 */}
          <div className="mb-4 flex justify-center gap-6 text-sm">
            <div className="flex items-center gap-1">
              <span className="text-gray-600">棋盘：</span>
              <span className="font-semibold">{boardSize}×{boardSize}</span>
            </div>
            <div className="flex items-center gap-1">
              <span className="text-gray-600">地雷：</span>
              <span className="font-semibold text-red-600">{mineCount} 个</span>
            </div>
            <div className="flex items-center gap-1">
              <span className="text-gray-600">剩余：</span>
              <span className="font-semibold text-blue-600">
                {board.length > 0 ? (boardSize * boardSize - mineCount - board.reduce((count, row) => count + row.filter(cell => cell.revealed && !cell.hasMine).length, 0)) : 0} 格
              </span>
            </div>
          </div>

          {/* 游戏板 */}
          <div className="flex justify-center mb-6">
            <div
              className="grid gap-1 p-4 bg-gray-100 rounded-lg shadow-inner"
              style={{
                gridTemplateColumns: `repeat(${boardSize}, 32px)`,
              }}
            >
              {board.map((row, x) =>
                row.map((cell, y) => (
                  <div
                    key={`${x}-${y}`}
                    className={`w-8 h-8 flex items-center justify-center border-2 cursor-pointer transition-all duration-150 hover:scale-105 ${getCellColor(cell)} ${
                      cell.revealed ? 'border-gray-400' : 'border-gray-500 hover:border-gray-600'
                    }`}
                    onClick={() => handleCellClick(x, y)}
                    onContextMenu={(e) => handleRightClick(e, x, y)}
                    title={cell.flagged ? '已标记为地雷' : cell.revealed ? (cell.hasMine ? '地雷！' : `周围有 ${cell.adjacentMines} 个地雷`) : '点击翻开'}
                  >
                    {cell.revealed && !cell.hasMine && cell.adjacentMines > 0 && (
                      <span className={`font-bold text-sm ${
                        cell.adjacentMines === 1 ? 'text-blue-600' :
                        cell.adjacentMines === 2 ? 'text-green-600' :
                        cell.adjacentMines === 3 ? 'text-red-600' :
                        cell.adjacentMines === 4 ? 'text-purple-600' :
                        cell.adjacentMines === 5 ? 'text-yellow-600' :
                        cell.adjacentMines === 6 ? 'text-pink-600' :
                        cell.adjacentMines === 7 ? 'text-black' : 'text-gray-600'
                      }`}>
                        {cell.adjacentMines}
                      </span>
                    )}
                    {cell.revealed && cell.hasMine && "💣"}
                    {!cell.revealed && cell.flagged && "🚩"}
                  </div>
                ))
              )}
            </div>
          </div>

          {/* 操作说明 */}
          <div className="mt-6 p-4 bg-gray-50 rounded-lg">
            <h3 className="text-sm font-semibold text-gray-700 mb-2 text-center">🎮 操作说明</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-xs text-gray-600">
              <div className="flex items-center gap-2">
                <span className="w-2 h-2 bg-blue-500 rounded-full"></span>
                <span>左键点击：翻开格子</span>
              </div>
              <div className="flex items-center gap-2">
                <span className="w-2 h-2 bg-red-500 rounded-full"></span>
                <span>右键点击：标记/取消标记地雷</span>
              </div>
              <div className="flex items-center gap-2">
                <span className="w-2 h-2 bg-green-500 rounded-full"></span>
                <span>数字：表示周围地雷数量</span>
              </div>
              <div className="flex items-center gap-2">
                <span className="w-2 h-2 bg-yellow-500 rounded-full"></span>
                <span>🚩：已标记的可疑地雷</span>
              </div>
            </div>
          </div>
        </>
      )}
      </div>
    </div>
  );
}
