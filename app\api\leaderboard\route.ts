import { getTopScores } from '@/lib/db';
import { NextResponse } from 'next/server';

export async function GET(request: Request) {
  try {
    console.log('API: Received leaderboard request');
    
    // Get limit from query parameters
    const { searchParams } = new URL(request.url);
    const limitParam = searchParams.get('limit');
    const limit = limitParam ? parseInt(limitParam, 10) : 10;
    
    if (isNaN(limit) || limit < 1 || limit > 100) {
      return NextResponse.json({ 
        success: false, 
        error: 'Invalid limit parameter. Must be between 1 and 100.' 
      }, { status: 400 });
    }

    console.log('API: Fetching top scores with limit:', limit);
    const scores = await getTopScores(limit);
    console.log('API: Retrieved scores:', scores.length);
    
    return NextResponse.json({ 
      success: true, 
      data: scores,
      count: scores.length 
    });
  } catch (error: any) {
    console.error('API leaderboard error details:', {
      message: error?.message || 'No message',
      stack: error?.stack || 'No stack',
      name: error?.name || 'No name',
      error: error
    });
    
    // Extract error message with fallbacks
    let errorMessage = 'An unknown error occurred';
    if (error instanceof Error) {
      errorMessage = error.message || 'Error object has no message';
    } else if (typeof error === 'string') {
      errorMessage = error;
    } else if (error && typeof error === 'object') {
      errorMessage = error.message || error.toString() || 'Object error with no message';
    }
    
    return NextResponse.json(
      { success: false, error: errorMessage },
      { status: 500 }
    );
  }
}
