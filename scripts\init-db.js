#!/usr/bin/env node

/**
 * Database initialization script
 * Creates the required table if it doesn't exist
 */

const { Pool } = require('pg');
require('dotenv').config({ path: '.env.local' });

async function initDatabase() {
  if (!process.env.DATABASE_URL) {
    console.error('❌ DATABASE_URL environment variable is required');
    console.log('📝 Please configure your .env.local file first');
    process.exit(1);
  }

  const pool = new Pool({
    connectionString: process.env.DATABASE_URL,
    connectionTimeoutMillis: 10000,
  });

  try {
    console.log('🔌 Connecting to database...');
    const client = await pool.connect();
    
    console.log('✅ Connected to database');
    
    // Check if table exists
    const tableCheck = await client.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name = 'newtable'
      );
    `);
    
    if (tableCheck.rows[0].exists) {
      console.log('✅ Table "newtable" already exists');
    } else {
      console.log('📝 Creating table "newtable"...');
      
      await client.query(`
        CREATE TABLE public.newtable (
          id SERIAL PRIMARY KEY,
          playname VARCHAR(255) NOT NULL,
          score INTEGER NOT NULL,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
      `);
      
      console.log('✅ Table "newtable" created successfully');
    }
    
    // Test insert and select
    console.log('🧪 Testing database operations...');
    
    const testName = 'TestPlayer_' + Date.now();
    const testScore = 42;
    
    await client.query(
      'INSERT INTO public.newtable (playname, score) VALUES ($1, $2)',
      [testName, testScore]
    );
    
    const result = await client.query(
      'SELECT * FROM public.newtable WHERE playname = $1',
      [testName]
    );
    
    if (result.rows.length > 0) {
      console.log('✅ Database operations test passed');
      
      // Clean up test data
      await client.query(
        'DELETE FROM public.newtable WHERE playname = $1',
        [testName]
      );
    } else {
      console.log('❌ Database operations test failed');
    }
    
    client.release();
    console.log('\n🎉 Database initialization completed successfully!');
    
  } catch (error) {
    console.error('❌ Database initialization failed:', error.message);
    
    if (error.message.includes('database') && error.message.includes('does not exist')) {
      console.log('\n💡 It looks like the database doesn\'t exist. Please create it first:');
      console.log('   CREATE DATABASE minesweeper_db;');
    } else if (error.message.includes('authentication')) {
      console.log('\n💡 Authentication failed. Please check your database credentials.');
    } else if (error.message.includes('connection')) {
      console.log('\n💡 Connection failed. Please check if PostgreSQL is running and accessible.');
    }
    
    process.exit(1);
  } finally {
    await pool.end();
  }
}

initDatabase();
