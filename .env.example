# Database Configuration
# PostgreSQL connection string
# Format: postgresql://username:password@host:port/database
DATABASE_URL=postgresql://username:password@localhost:5432/minesweeper_db

# Next.js Configuration
# Set to 'development' for local development, 'production' for production
NODE_ENV=development

# Application Configuration
# Base URL for the application (useful for API calls)
NEXT_PUBLIC_APP_URL=http://localhost:3000

# Security Configuration
# Secret key for session management (generate a random string for production)
NEXTAUTH_SECRET=your-secret-key-here
NEXTAUTH_URL=http://localhost:3000

# Optional: Database Pool Configuration
# Maximum number of database connections in the pool
DB_POOL_MAX=20
# Minimum number of database connections in the pool
DB_POOL_MIN=5
# Connection timeout in milliseconds
DB_CONNECTION_TIMEOUT=30000
