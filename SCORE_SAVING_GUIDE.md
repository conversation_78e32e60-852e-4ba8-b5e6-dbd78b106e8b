# 分数保存到排行榜指南

## 概述

本指南将帮助您确保用户得分能够正确保存到排行榜中，并提供故障排除方法。

## 分数保存流程

### 1. 游戏结束触发
当游戏结束时（获胜或失败），系统会自动：
- 计算最终分数
- 记录游戏时间
- 调用 `saveScore` 函数
- 自动显示排行榜

### 2. 分数计算
```javascript
// 获胜分数计算
baseScore (100/200/300) + 
mineBonus (地雷数 × 5) + 
timeBonus (1000 / 游戏时间秒) + 
perfectBonus (无错误标记 +50)

// 失败分数计算
翻开的安全格子数量
```

### 3. 数据保存
分数会保存以下信息到数据库：
- 玩家姓名
- 分数
- 棋盘大小
- 地雷数量
- 是否获胜
- 游戏时间

## 用户操作指南

### 开始游戏
1. 输入玩家姓名
2. 选择难度设置
3. 点击"开始游戏"

### 游戏结束
1. 游戏结束后会自动保存分数
2. 显示保存状态消息
3. 自动显示排行榜
4. 可以查看自己的排名

### 查看排行榜
- **主菜单**：点击"🏆 排行榜"按钮
- **游戏中**：点击游戏界面的排行榜按钮
- **自动显示**：游戏结束后自动显示

## 测试功能

### 手动测试
1. 在主菜单输入玩家姓名
2. 点击"🧪 测试保存"按钮
3. 系统会生成随机分数并保存
4. 检查排行榜是否更新

### 自动测试
```bash
# 启动开发服务器
npm run dev

# 在另一个终端运行测试
npm run test-score-save
```

## 故障排除

### 问题 1: 分数没有保存
**症状**: 游戏结束后没有看到"分数保存成功"消息

**检查步骤**:
1. 确认已输入玩家姓名
2. 检查浏览器控制台是否有错误
3. 检查网络连接

**解决方案**:
```bash
# 检查环境配置
npm run check-env

# 检查数据库连接
npm run debug-db

# 测试分数保存
npm run test-score-save
```

### 问题 2: 排行榜为空
**症状**: 点击排行榜按钮显示"暂无排行榜数据"

**可能原因**:
- 数据库连接问题
- 数据库表不存在
- 没有保存过分数

**解决方案**:
```bash
# 初始化数据库表
npm run init-db

# 测试保存一些分数
npm run test-score-save
```

### 问题 3: 数据库连接失败
**症状**: 显示"数据库未连接，分数未保存（开发模式）"

**解决方案**:
1. **配置数据库**:
   ```bash
   # 编辑 .env.local 文件
   DATABASE_URL=postgresql://username:password@localhost:5432/minesweeper_db
   ```

2. **启动 PostgreSQL**:
   - Windows: 启动 PostgreSQL 服务
   - macOS: `brew services start postgresql`
   - Linux: `sudo systemctl start postgresql`

3. **创建数据库**:
   ```sql
   CREATE DATABASE minesweeper_db;
   ```

4. **初始化表**:
   ```bash
   npm run init-db
   ```

### 问题 4: 分数显示不正确
**症状**: 排行榜中的分数或信息显示异常

**检查**:
1. 数据库表结构是否正确
2. 分数计算逻辑是否正常

**解决方案**:
```bash
# 重新初始化数据库表
npm run init-db

# 清除旧数据（如果需要）
# 手动连接数据库并清空表
```

## 开发模式说明

### 模拟数据
如果没有配置数据库，系统会：
- 显示模拟排行榜数据
- 在控制台记录分数保存操作
- 提示用户配置数据库

### 生产环境
在生产环境中：
- 必须配置真实数据库
- 分数会永久保存
- 支持多用户排行榜

## 最佳实践

### 用户体验
1. **及时反馈**: 分数保存后立即显示结果
2. **自动刷新**: 保存成功后自动刷新排行榜
3. **错误提示**: 清晰的错误消息和解决建议

### 数据完整性
1. **输入验证**: 严格验证所有输入参数
2. **错误处理**: 完善的错误处理和回退机制
3. **日志记录**: 详细的操作日志便于调试

### 性能优化
1. **连接池**: 使用数据库连接池
2. **缓存**: 考虑添加排行榜缓存
3. **分页**: 大量数据时使用分页加载

## 常用命令

```bash
# 环境检查
npm run check-env

# 数据库调试
npm run debug-db

# 初始化数据库
npm run init-db

# 测试分数保存
npm run test-score-save

# 启动开发服务器
npm run dev
```

## 技术支持

如果遇到问题：
1. 查看浏览器控制台错误
2. 检查开发服务器终端输出
3. 运行相关测试脚本
4. 参考故障排除部分

记住：分数保存功能依赖于正确的数据库配置。确保按照环境设置指南正确配置数据库连接。
