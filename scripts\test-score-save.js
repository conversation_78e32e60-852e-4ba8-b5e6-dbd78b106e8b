#!/usr/bin/env node

/**
 * Score saving test script
 * Tests the complete flow from saving scores to retrieving leaderboard
 */

const testScores = [
  {
    name: 'TestPlayer1',
    score: 350,
    boardSize: 10,
    mineCount: 15,
    isWin: true,
    gameTimeSeconds: 120
  },
  {
    name: 'TestPlayer2',
    score: 285,
    boardSize: 8,
    mineCount: 10,
    isWin: true,
    gameTimeSeconds: 95
  },
  {
    name: 'TestPlayer3',
    score: 170,
    boardSize: 12,
    mineCount: 20,
    isWin: false,
    gameTimeSeconds: 200
  },
  {
    name: 'TestPlayer4',
    score: 420,
    boardSize: 12,
    mineCount: 20,
    isWin: true,
    gameTimeSeconds: 180
  }
];

async function testScoreSaving() {
  const baseUrl = 'http://localhost:3000';
  
  console.log('🧪 Testing score saving and leaderboard functionality...\n');
  
  // Test 1: Save multiple test scores
  console.log('📝 Step 1: Saving test scores...');
  for (const testScore of testScores) {
    try {
      console.log(`   Saving: ${testScore.name} - ${testScore.score} points`);
      
      const response = await fetch(`${baseUrl}/api/save-score`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(testScore),
      });
      
      const data = await response.json();
      
      if (response.ok && data.success) {
        console.log(`   ✅ Saved successfully`);
      } else {
        console.log(`   ❌ Failed: ${data.error || 'Unknown error'}`);
      }
    } catch (error) {
      console.log(`   ❌ Error: ${error.message}`);
    }
  }
  
  console.log('\n⏳ Waiting 2 seconds for database to update...\n');
  await new Promise(resolve => setTimeout(resolve, 2000));
  
  // Test 2: Retrieve leaderboard
  console.log('📊 Step 2: Retrieving leaderboard...');
  try {
    const response = await fetch(`${baseUrl}/api/leaderboard?limit=10`);
    const data = await response.json();
    
    if (response.ok && data.success) {
      console.log(`✅ Retrieved ${data.count} scores from leaderboard:`);
      console.log('\n🏆 Current Leaderboard:');
      console.log('─'.repeat(80));
      console.log('Rank | Player      | Score | Difficulty | Win | Time');
      console.log('─'.repeat(80));
      
      data.data.forEach((entry, index) => {
        const rank = index + 1;
        const player = entry.playname.padEnd(11);
        const score = entry.score.toString().padStart(5);
        const difficulty = `${entry.board_size || 10}x${entry.board_size || 10}`.padEnd(10);
        const win = entry.is_win ? '✅' : '❌';
        const time = `${entry.game_time_seconds || 0}s`.padStart(5);
        
        console.log(`${rank.toString().padStart(4)} | ${player} | ${score} | ${difficulty} | ${win}  | ${time}`);
      });
      console.log('─'.repeat(80));
      
      // Verify our test scores are in the leaderboard
      const testPlayerNames = testScores.map(s => s.name);
      const foundTestPlayers = data.data.filter(entry => 
        testPlayerNames.includes(entry.playname)
      );
      
      console.log(`\n✅ Found ${foundTestPlayers.length}/${testScores.length} test players in leaderboard`);
      
      if (foundTestPlayers.length === testScores.length) {
        console.log('🎉 All test scores successfully saved to leaderboard!');
      } else {
        console.log('⚠️  Some test scores may not have been saved properly');
      }
      
    } else {
      console.log(`❌ Failed to retrieve leaderboard: ${data.error || 'Unknown error'}`);
    }
  } catch (error) {
    console.log(`❌ Error retrieving leaderboard: ${error.message}`);
  }
  
  // Test 3: Clean up test data (optional)
  console.log('\n🧹 Step 3: Cleaning up test data...');
  console.log('Note: Test data cleanup requires direct database access');
  console.log('You can manually remove test entries if needed');
  
  console.log('\n✅ Score saving test completed!');
  console.log('\n💡 Tips:');
  console.log('   - If scores are not appearing, check database connection');
  console.log('   - Run "npm run init-db" if table doesn\'t exist');
  console.log('   - Check browser console for detailed error messages');
}

// Check if server is running
async function checkServer() {
  try {
    const response = await fetch('http://localhost:3000');
    if (response.ok) {
      console.log('✅ Server is running\n');
      return true;
    }
  } catch (error) {
    console.log('❌ Server is not running. Please start the development server first:');
    console.log('   npm run dev\n');
    return false;
  }
}

async function main() {
  const serverRunning = await checkServer();
  if (serverRunning) {
    await testScoreSaving();
  }
}

main().catch(console.error);
